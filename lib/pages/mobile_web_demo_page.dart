import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../components/web_page.dart';

class MobileWebDemoPage extends StatelessWidget {
  const MobileWebDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('移动端 WebView 测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (kIsWeb) ...[
              // Web平台提示
              Card(
                color: Colors.orange[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 48,
                        color: Colors.orange[700],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '当前运行在 Web 平台',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'WebView 功能需要在移动设备或桌面应用中测试',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '请使用以下命令在移动设备上测试：',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'flutter run -d <device_id>',
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            const Text(
              'WebView 组件功能测试',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // 测试按钮
            _buildTestButton(
              context,
              title: '百度搜索',
              url: 'https://www.baidu.com',
              icon: Icons.search,
              color: Colors.blue,
              description: '测试中文网站和搜索功能',
            ),
            const SizedBox(height: 16),
            
            _buildTestButton(
              context,
              title: '测试',
              // url: 'http://*************:8000/pages/pageA/',
              url: 'http://*************:5173/pages/about/',
              // url: 'http://*************:5173/pages/notice/index.html?id=1',
              icon: Icons.code,
              color: Colors.black87,
              description: 'H5测试页面',
            ),
            const SizedBox(height: 16),
            
            // _buildTestButton(
            //   context,
            //   title: 'Flutter 官网',
            //   url: 'https://flutter.dev',
            //   icon: Icons.flutter_dash,
            //   color: Colors.blue[600]!,
            //   description: '测试官方文档网站',
            // ),
            // const SizedBox(height: 16),
            
            // _buildTestButton(
            //   context,
            //   title: '新浪新闻',
            //   url: 'https://news.sina.com.cn',
            //   icon: Icons.newspaper,
            //   color: Colors.red,
            //   description: '测试新闻网站和页面跳转',
            // ),
            
            const Spacer(),
            
            // 功能说明
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb_outline, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        Text(
                          '测试要点：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text('1. 观察标题是否随页面切换而更新'),
                    const Text('2. 测试前进/后退按钮功能'),
                    const Text('3. 检查页面加载进度显示'),
                    const Text('4. 验证刷新功能是否正常'),
                    const Text('5. 测试错误页面的处理'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    BuildContext context, {
    required String title,
    required String url,
    required IconData icon,
    required Color color,
    required String description,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => WebPage(
                url: url,
                title: null, // 不设置固定标题，让它动态更新
                onPageStarted: (url) {
                  debugPrint('🚀 开始加载: $url');
                },
                onPageFinished: (url) {
                  debugPrint('✅ 加载完成: $url');
                },
                onWebResourceError: (error) {
                  debugPrint('❌ 加载错误: ${error.description}');
                },
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }
}