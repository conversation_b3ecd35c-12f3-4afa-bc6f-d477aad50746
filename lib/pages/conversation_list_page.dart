import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import '../services/im_service.dart';
import '../models/im_models.dart';
import '../utils/im_test_config.dart';
import 'chat_detail_page.dart';

class ConversationListPage extends StatefulWidget {
  const ConversationListPage({super.key});

  @override
  State<ConversationListPage> createState() => _ConversationListPageState();
}

class _ConversationListPageState extends State<ConversationListPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeIM();
    });
  }

  Future<void> _initializeIM() async {
    final imService = Provider.of<IMService>(context, listen: false);
    
    if (!imService.isInitialized) {
      await imService.initializeIM();
    }
    
    if (!imService.isLoggedIn) {
      // 尝试自动登录
      bool autoLoginSuccess = await imService.autoLogin();
      if (!autoLoginSuccess) {
        // 如果自动登录失败，显示登录对话框
        _showLoginDialog();
      }
    }
  }

  void _showLoginDialog() {
    final userIdController = TextEditingController();
    final userSigController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('IM登录'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: userIdController,
              decoration: const InputDecoration(
                labelText: '用户ID',
                hintText: '请输入用户ID',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: userSigController,
              decoration: const InputDecoration(
                labelText: 'UserSig',
                hintText: '请输入UserSig',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            const Text(
              '注意：请先在腾讯云控制台配置SDKAppID，并生成UserSig',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            ),
            const SizedBox(height: 8),
            Text(
              IMTestConfig.getTestUserTip(),
              style: const TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              final userId = userIdController.text.trim();
              final userSig = userSigController.text.trim();
              
              if (userId.isEmpty || userSig.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请填写完整信息')),
                );
                return;
              }
              
              Navigator.of(context).pop();
              
              final imService = Provider.of<IMService>(context, listen: false);
              bool success = await imService.login(userId, userSig);
              
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('登录成功')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('登录失败，请检查用户ID和UserSig')),
                );
              }
            },
            child: const Text('登录'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('聊天'),
        automaticallyImplyLeading: false,
        actions: [
          Consumer<IMService>(
            builder: (context, imService, child) {
              if (imService.isLoggedIn) {
                return PopupMenuButton<String>(
                  onSelected: (value) async {
                    switch (value) {
                      case 'logout':
                        await imService.logout();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('已退出登录')),
                        );
                        break;
                      case 'refresh':
                        await imService.getConversationList();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'refresh',
                      child: Text('刷新'),
                    ),
                    const PopupMenuItem(
                      value: 'logout',
                      child: Text('退出登录'),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<IMService>(
        builder: (context, imService, child) {
          if (!imService.isInitialized) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('初始化中...'),
                ],
              ),
            );
          }

          if (!imService.isLoggedIn) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.chat_bubble_outline,
                    size: 80,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '未登录',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _showLoginDialog,
                    child: const Text('登录'),
                  ),
                ],
              ),
            );
          }

          if (imService.conversationList.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '暂无会话',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '开始一段新的对话吧',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await imService.getConversationList();
            },
            child: ListView.builder(
              itemCount: imService.conversationList.length,
              itemBuilder: (context, index) {
                final conversation = ChatConversation.fromV2TimConversation(
                  imService.conversationList[index],
                );
                
                return _buildConversationItem(conversation);
              },
            ),
          );
        },
      ),
      floatingActionButton: Consumer<IMService>(
        builder: (context, imService, child) {
          if (imService.isLoggedIn) {
            return FloatingActionButton(
              onPressed: _showNewChatDialog,
              child: const Icon(Icons.add),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildConversationItem(ChatConversation conversation) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: conversation.avatar != null 
            ? NetworkImage(conversation.avatar!) 
            : null,
        child: conversation.avatar == null 
            ? Text(conversation.displayName.isNotEmpty 
                ? conversation.displayName[0].toUpperCase() 
                : '?')
            : null,
      ),
      title: Text(
        conversation.displayName,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        conversation.lastMessageText,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 13,
        ),
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            _formatTime(conversation.lastMessageTime),
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
          if (conversation.unreadCount > 0) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                conversation.unreadCount > 99 ? '99+' : '${conversation.unreadCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ],
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatDetailPage(
              conversation: conversation,
            ),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  void _showNewChatDialog() {
    final userIdController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('新建聊天'),
        content: TextField(
          controller: userIdController,
          decoration: const InputDecoration(
            labelText: '用户ID',
            hintText: '请输入要聊天的用户ID',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              final userId = userIdController.text.trim();
              if (userId.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入用户ID')),
                );
                return;
              }
              
              Navigator.of(context).pop();
              
              // 创建一个临时会话对象
              final tempConversation = ChatConversation(
                originalConversation: V2TimConversation(
                  conversationID: 'c2c_$userId',
                  userID: userId,
                  type: 1, // V2TIM_C2C
                ),
                displayName: userId,
                avatar: null,
                lastMessageText: '',
                lastMessageTime: DateTime.now(),
                unreadCount: 0,
                isGroup: false,
              );
              
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatDetailPage(
                    conversation: tempConversation,
                  ),
                ),
              );
            },
            child: const Text('开始聊天'),
          ),
        ],
      ),
    );
  }
}