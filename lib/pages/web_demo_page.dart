import 'package:flutter/material.dart';
import '../utils/web_utils.dart';

class WebDemoPage extends StatelessWidget {
  const WebDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebView 示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'WebView 组件使用示例',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // 预设网站按钮
            _buildWebButton(
              context,
              title: '百度',
              url: 'https://www.baidu.com',
              icon: Icons.search,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            
            _buildWebButton(
              context,
              title: 'GitHub',
              url: 'https://github.com',
              icon: Icons.code,
              color: Colors.black,
            ),
            const SizedBox(height: 16),
            
            _buildWebButton(
              context,
              title: 'Flutter 官网',
              url: 'https://flutter.dev',
              icon: Icons.flutter_dash,
              color: Colors.blue[600]!,
            ),
            const SizedBox(height: 32),
            
            // 自定义URL输入
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      '自定义URL',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: '输入网址',
                        hintText: 'https://example.com',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.link),
                      ),
                      onSubmitted: (url) {
                        if (url.isNotEmpty) {
                          _openCustomUrl(context, url);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        _showUrlInputDialog(context);
                      },
                      icon: const Icon(Icons.open_in_browser),
                      label: const Text('打开自定义网址'),
                    ),
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // 功能说明
            Card(
              color: Colors.grey[100],
              child: const Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '功能特性：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('• 支持前进/后退导航'),
                    Text('• 页面加载进度显示'),
                    Text('• 刷新和重新加载'),
                    Text('• 错误处理和重试'),
                    Text('• 自定义标题和配置'),
                    Text('• 响应式设计'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebButton(
    BuildContext context, {
    required String title,
    required String url,
    required IconData icon,
    required Color color,
  }) {
    return ElevatedButton.icon(
      onPressed: () {
        WebUtils.openWebPage(
          context,
          url: url,
          title: title,
          onPageStarted: (url) {
            print('开始加载: $url');
          },
          onPageFinished: (url) {
            print('加载完成: $url');
          },
        );
      },
      icon: Icon(icon, color: Colors.white),
      label: Text(
        '打开 $title',
        style: const TextStyle(color: Colors.white),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _openCustomUrl(BuildContext context, String url) {
    final formattedUrl = WebUtils.formatUrl(url);
    
    if (!WebUtils.isValidUrl(formattedUrl)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入有效的网址'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    WebUtils.openWebPage(
      context,
      url: formattedUrl,
      title: WebUtils.extractDomain(formattedUrl) ?? '自定义页面',
    );
  }

  void _showUrlInputDialog(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('输入网址'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'URL',
            hintText: 'https://example.com',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (controller.text.isNotEmpty) {
                _openCustomUrl(context, controller.text);
              }
            },
            child: const Text('打开'),
          ),
        ],
      ),
    );
  }
}