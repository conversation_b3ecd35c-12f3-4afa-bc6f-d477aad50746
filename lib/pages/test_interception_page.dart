import 'package:flutter/material.dart';
import '../components/web_page.dart';

class TestInterceptionPage extends StatefulWidget {
  const TestInterceptionPage({super.key});

  @override
  State<TestInterceptionPage> createState() => _TestInterceptionPageState();
}

class _TestInterceptionPageState extends State<TestInterceptionPage> {
  final List<String> _interceptedUrls = [];
  int _totalInterceptions = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('资源拦截测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 统计信息卡片
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('总拦截次数', _totalInterceptions.toString(), Colors.blue),
                        _buildStatItem('拦截URL数', _interceptedUrls.length.toString(), Colors.green),
                      ],
                    ),
                    if (_interceptedUrls.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text(
                        '已拦截的资源:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      ...(_interceptedUrls.take(3).map((url) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          '• ${_getShortUrl(url)}',
                          style: const TextStyle(fontSize: 12, color: Colors.green),
                        ),
                      ))),
                      if (_interceptedUrls.length > 3)
                        Text(
                          '... 还有 ${_interceptedUrls.length - 3} 个',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // 测试按钮
            const Text(
              '选择测试页面:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            _buildTestButton(
              '测试Vue CDN拦截',
              '包含Vue.js CDN引用的测试页面',
              Icons.code,
              Colors.green,
              () => _openTestPage(_createVueTestHtml()),
            ),
            const SizedBox(height: 12),
            
            _buildTestButton(
              '测试多个CDN拦截',
              '包含多个CDN资源的复杂页面',
              Icons.library_books,
              Colors.orange,
              () => _openTestPage(_createMultiCdnTestHtml()),
            ),
            const SizedBox(height: 12),
            
            _buildTestButton(
              '清除统计',
              '重置拦截统计数据',
              Icons.refresh,
              Colors.grey,
              _clearStats,
            ),
            
            const Spacer(),
            
            // 说明信息
            Card(
              color: Colors.amber[50],
              child: const Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📝 测试说明:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. 点击测试按钮打开包含CDN资源的页面'),
                    Text('2. 观察标题栏的拦截计数器'),
                    Text('3. 查看控制台日志确认拦截成功'),
                    Text('4. 对比加载速度差异'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildTestButton(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      description,
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  void _openTestPage(String htmlContent) {
    final dataUri = Uri.dataFromString(
      htmlContent,
      mimeType: 'text/html',
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebPage(
          url: dataUri.toString(),
          title: null,
          enableResourceInterception: true,
          onResourceIntercepted: (url, success) {
            setState(() {
              if (success) {
                _totalInterceptions++;
                if (!_interceptedUrls.contains(url)) {
                  _interceptedUrls.add(url);
                }
              }
            });
          },
          onPageFinished: (url) {
            debugPrint('页面加载完成，总拦截次数: $_totalInterceptions');
          },
        ),
      ),
    );
  }

  void _clearStats() {
    setState(() {
      _interceptedUrls.clear();
      _totalInterceptions = 0;
    });
  }

  String _getShortUrl(String url) {
    if (url.length > 50) {
      return '${url.substring(0, 47)}...';
    }
    return url;
  }

  String _createVueTestHtml() {
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue CDN 拦截测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .loading { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vue.js CDN 拦截测试</h1>
        <div id="app">
            <div class="status loading">正在加载 Vue.js...</div>
        </div>
        
        <div class="status">
            <h3>测试说明:</h3>
            <p>此页面尝试从CDN加载Vue.js 3.5.17</p>
            <p>如果拦截成功，将使用本地缓存版本</p>
            <p>观察标题栏的拦截计数器变化</p>
        </div>
    </div>

    <script type="module">
        console.log('开始加载Vue.js...');
        
        try {
            // 尝试加载Vue.js CDN
            const { createApp } = await import('https://unpkg.com/vue@3.5.17/dist/vue.esm-browser.prod.js');
            
            console.log('Vue.js加载成功！');
            
            const app = createApp({
                data() {
                    return {
                        message: 'Vue.js 加载成功！',
                        loadTime: new Date().toLocaleTimeString(),
                        intercepted: true
                    }
                },
                template: \`
                    <div class="status success">
                        <h2>{{ message }}</h2>
                        <p>加载时间: {{ loadTime }}</p>
                        <p>✅ CDN请求已被拦截并使用本地资源</p>
                        <p>Vue版本: {{ \$options.version || 'Unknown' }}</p>
                    </div>
                \`
            });
            
            app.mount('#app');
            
        } catch (error) {
            console.error('Vue.js加载失败:', error);
            document.getElementById('app').innerHTML = \`
                <div class="status" style="background: #f8d7da; color: #721c24;">
                    <h2>❌ Vue.js 加载失败</h2>
                    <p>错误: \${error.message}</p>
                </div>
            \`;
        }
    </script>
</body>
</html>
    ''';
  }

  String _createMultiCdnTestHtml() {
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多CDN拦截测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .resource { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .intercepted { background: #d4edda; color: #155724; }
        .normal { background: #f8f9fa; color: #495057; }
    </style>
</head>
<body>
    <h1>多CDN资源拦截测试</h1>
    <div id="results"></div>
    
    <script type="module">
        const resources = [
            'https://unpkg.com/vue@3.5.17/dist/vue.esm-browser.prod.js',
            'https://unpkg.com/vue@3/dist/vue.esm-browser.prod.js',
            'https://cdn.jsdelivr.net/npm/vue@3.5.17/dist/vue.esm-browser.prod.js'
        ];
        
        const results = document.getElementById('results');
        
        for (const url of resources) {
            const div = document.createElement('div');
            div.className = 'resource normal';
            div.innerHTML = \`
                <strong>测试资源:</strong> \${url}<br>
                <span>状态: 正在测试...</span>
            \`;
            results.appendChild(div);
            
            try {
                const startTime = performance.now();
                await import(url);
                const endTime = performance.now();
                
                div.className = 'resource intercepted';
                div.innerHTML = \`
                    <strong>✅ 拦截成功:</strong> \${url}<br>
                    <span>加载时间: \${(endTime - startTime).toFixed(2)}ms</span>
                \`;
            } catch (error) {
                div.innerHTML = \`
                    <strong>❌ 加载失败:</strong> \${url}<br>
                    <span>错误: \${error.message}</span>
                \`;
            }
        }
    </script>
</body>
</html>
    ''';
  }
}