import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:xk_app/sdk/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:xk_app/components/auth_header.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String _selectedLanguage = '中文';
  bool _isLoading = false;
  
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _initializeDefaultValues();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 初始化默认值（仅在开发环境）
  void _initializeDefaultValues() {
    if (kDebugMode) {
      // 开发环境下设置默认值
      _emailController.text = '<EMAIL>';
      _passwordController.text = '123123';
      _confirmPasswordController.text = '123123';
    }
  }

  // 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // 验证密码强度
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  // 验证密码确认
  bool _isPasswordMatch() {
    return _passwordController.text == _confirmPasswordController.text;
  }

  // 注册逻辑
  Future<void> _register() async {
    // 验证输入
    if (_emailController.text.isEmpty) {
      _showErrorSnackBar('请输入邮箱');
      return;
    }

    if (!_isValidEmail(_emailController.text)) {
      _showErrorSnackBar('请输入有效的邮箱地址');
      return;
    }

    if (_passwordController.text.isEmpty) {
      _showErrorSnackBar('请输入密码');
      return;
    }

    if (!_isValidPassword(_passwordController.text)) {
      _showErrorSnackBar('密码长度至少6位');
      return;
    }

    if (_confirmPasswordController.text.isEmpty) {
      _showErrorSnackBar('请确认密码');
      return;
    }

    if (!_isPasswordMatch()) {
      _showErrorSnackBar('两次输入的密码不一致');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 调用注册API - 注意API需要验证码，这里暂时使用空字符串
      // 在实际应用中，应该先获取验证码
      final response = await _apiService.user.register(
        email: _emailController.text,
        pwd: _passwordController.text,
        code: '', // 验证码字段，实际应用中需要先获取
      );

      if (response.isSuccess) {
        // 注册成功，保存用户信息
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_token', response.data ?? '');
        await prefs.setString('user_email', _emailController.text);

        if (mounted) {
          _showSuccessSnackBar('注册成功！');
          // 延迟跳转到完善资料页面
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/profile-setup');
            }
          });
        }
      } else {
        _showErrorSnackBar(response.msg ?? '注册失败，请重试');
      }
    } catch (e) {
      if (kDebugMode) {
        print('注册错误: $e');
      }
      _showErrorSnackBar('网络错误，请检查网络连接');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 显示错误提示
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // 显示成功提示
  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // 跳转到登录页面
  void _goToLogin() {
    Navigator.pushReplacementNamed(context, '/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFE0E7FF),
              Color(0xFFF0F9FF),
              Color(0xFFECFDF5),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // 顶部头部组件
                SizedBox(
                  height: 200,
                  child: AuthHeader(
                    welcomeText: 'Hi\n欢迎注册\nFoxyra',
                    initialLanguage: _selectedLanguage,
                    onLanguageChanged: (language) {
                      setState(() {
                        _selectedLanguage = language;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 40),
                
                // 中间输入区域
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // 邮箱输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: const InputDecoration(
                              labelText: '邮箱',
                              hintText: '请输入邮箱地址',
                              prefixIcon: Icon(Icons.email_outlined),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(16),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // 密码输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            decoration: InputDecoration(
                              labelText: '密码',
                              hintText: '请输入密码（至少6位）',
                              prefixIcon: const Icon(Icons.lock_outline),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword ? Icons.visibility_off : Icons.visibility,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.all(16),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // 确认密码输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _confirmPasswordController,
                            obscureText: _obscureConfirmPassword,
                            decoration: InputDecoration(
                              labelText: '确认密码',
                              hintText: '请再次输入密码',
                              prefixIcon: const Icon(Icons.lock_outline),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureConfirmPassword = !_obscureConfirmPassword;
                                  });
                                },
                              ),
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.all(16),
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),
                        
                        // 注册按钮
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _register,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6366F1),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Text(
                                    '注册',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        
                        // 已有账号，去登录
                        TextButton(
                          onPressed: _goToLogin,
                          child: const Text(
                            '已有账号，去登录',
                            style: TextStyle(
                              color: Color(0xFF6366F1),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}