import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileSetupPage extends StatefulWidget {
  const ProfileSetupPage({super.key});

  @override
  State<ProfileSetupPage> createState() => _ProfileSetupPageState();
}

class _ProfileSetupPageState extends State<ProfileSetupPage> {
  final TextEditingController _nicknameController = TextEditingController();
  DateTime? _selectedDate;
  String _selectedCountry = '中国';
  int _selectedGender = 2; // 0: 男性, 1: 女性, 2: 无性别
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeDefaultValues();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  // 初始化默认值
  void _initializeDefaultValues() {
    if (kDebugMode) {
      _nicknameController.text = 'alune';
      _selectedDate = DateTime(1992, 5, 15);
    }
  }

  // 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime(1992, 5, 15),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('zh', 'CN'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  // 选择国家
  Future<void> _selectCountry() async {
    final countries = ['中国', '美国', '日本', '韩国', '英国', '法国', '德国', '澳大利亚'];
    final String? selected = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择国家'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: countries.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(countries[index]),
                  onTap: () {
                    Navigator.of(context).pop(countries[index]);
                  },
                );
              },
            ),
          ),
        );
      },
    );
    if (selected != null) {
      setState(() {
        _selectedCountry = selected;
      });
    }
  }

  // 格式化日期显示
  String _formatDate(DateTime? date) {
    if (date == null) return '请选择';
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // 提交资料
  Future<void> _submitProfile() async {
    if (_nicknameController.text.trim().isEmpty) {
      _showErrorSnackBar('请输入昵称');
      return;
    }

    if (_selectedDate == null) {
      _showErrorSnackBar('请选择出生日期');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 保存用户资料到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_nickname', _nicknameController.text.trim());
      await prefs.setString('user_birthdate', _formatDate(_selectedDate));
      await prefs.setString('user_country', _selectedCountry);
      await prefs.setInt('user_gender', _selectedGender);
      await prefs.setBool('profile_completed', true);

      if (mounted) {
        _showSuccessSnackBar('资料完善成功！');
        // 延迟跳转到主页面
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/main');
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('保存资料错误: $e');
      }
      _showErrorSnackBar('保存失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 显示错误提示
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // 显示成功提示
  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE0E7FF),
              Color(0xFFF0F9FF),
              Color(0xFFECFDF5),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // 顶部返回按钮
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Color(0xFF2D3748),
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // 头像区域
                  Stack(
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(60),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 60,
                          color: Color(0xFF6366F1),
                        ),
                      ),
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(18),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            size: 20,
                            color: Color(0xFF6366F1),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 40),
                  
                  // 信息输入区域
                  Column(
                    children: [
                      // 昵称输入
                      _buildInfoItem(
                        label: '怎么称呼你',
                        value: _nicknameController.text.isEmpty ? '请输入昵称' : _nicknameController.text,
                        onTap: () => _showNicknameDialog(),
                        isEditable: true,
                      ),
                      const SizedBox(height: 16),
                      
                      // 出生日期
                      _buildInfoItem(
                        label: '出生年月日',
                        value: _formatDate(_selectedDate),
                        onTap: _selectDate,
                        isEditable: true,
                      ),
                      const SizedBox(height: 16),
                      
                      // 所在国家
                      _buildInfoItem(
                        label: '所在国家',
                        value: _selectedCountry,
                        onTap: _selectCountry,
                        isEditable: true,
                      ),
                    ],
                  ),
                  const SizedBox(height: 40),
                  
                  // 性别选择
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildGenderOption(
                        icon: Icons.male,
                        label: '男性',
                        isSelected: _selectedGender == 0,
                        onTap: () => setState(() => _selectedGender = 0),
                      ),
                      _buildGenderOption(
                        icon: Icons.female,
                        label: '女性',
                        isSelected: _selectedGender == 1,
                        onTap: () => setState(() => _selectedGender = 1),
                      ),
                      _buildGenderOption(
                        icon: Icons.person_outline,
                        label: '无性别',
                        isSelected: _selectedGender == 2,
                        onTap: () => setState(() => _selectedGender = 2),
                      ),
                    ],
                  ),
                  const SizedBox(height: 60),
                  
                  // 提交按钮
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFD295F8),
                        foregroundColor: const Color(0xFF261F3E),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        elevation: 2,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF261F3E)),
                              ),
                            )
                          : const Text(
                              '提交',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建信息项
  Widget _buildInfoItem({
    required String label,
    required String value,
    required VoidCallback onTap,
    bool isEditable = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF261F3E),
              ),
            ),
            const Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                color: value.contains('请') ? Colors.grey : const Color(0xFF79797A),
              ),
            ),
            if (isEditable) ...[
              const SizedBox(width: 8),
              const Icon(
                Icons.chevron_right,
                color: Color(0xFF79797A),
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // 构建性别选项
  Widget _buildGenderOption({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        height: 120,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF3EEF9) : Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(28),
          border: isSelected 
              ? Border.all(color: const Color(0xFFA28FDB), width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: const Color(0xFF6366F1),
            ),
            const SizedBox(height: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF261F3E),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示昵称输入对话框
  Future<void> _showNicknameDialog() async {
    final TextEditingController dialogController = TextEditingController(text: _nicknameController.text);
    
    final String? result = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('输入昵称'),
          content: TextField(
            controller: dialogController,
            decoration: const InputDecoration(
              hintText: '请输入您的昵称',
              border: OutlineInputBorder(),
            ),
            maxLength: 20,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (dialogController.text.trim().isNotEmpty) {
                  Navigator.of(context).pop(dialogController.text.trim());
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
    
    if (result != null) {
      setState(() {
        _nicknameController.text = result;
      });
    }
    
    dialogController.dispose();
  }
}