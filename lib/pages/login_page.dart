import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:xk_app/sdk/index.dart';
import 'package:xk_app/sdk/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:xk_app/components/auth_header.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _obscurePassword = true;
  String _selectedLanguage = '中文';
  bool _isLoading = false;
  
  final List<String> _languages = ['中文', 'English', '日本語', '한국어'];
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _initializeDefaultValues();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // 初始化默认值（仅在开发环境）
  void _initializeDefaultValues() {
    if (kDebugMode) {
      // 开发环境下设置默认值
      _emailController.text = '<EMAIL>';
      _passwordController.text = '123123';
    }
  }

  // 登录方法
  Future<void> _login() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();

    // 表单验证
    if (email.isEmpty) {
      _showErrorDialog('请输入邮箱');
      return;
    }
    
    if (!_isValidEmail(email)) {
      _showErrorDialog('请输入有效的邮箱地址');
      return;
    }

    if (password.isEmpty) {
      _showErrorDialog('请输入密码');
      return;
    }

    if (password.length < 6) {
      _showErrorDialog('密码长度不能少于6位');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 调用登录API
      final response = await _apiService.user.login(
        email: email,
        pwd: password,
      );

      if (response.isSuccess && response.data != null) {
        // 登录成功，保存token
        await _saveToken(response.data!);
        
        // 登录成功后返回上一页
        if (mounted) {
          print('登录成功，准备返回');
          print('Navigator.canPop: ${Navigator.canPop(context)}');
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          } else {
            print('无法返回，导航栈为空，跳转到主页');
            Navigator.of(context).pushReplacementNamed('/home');
          }
        }
      } else {
        _showErrorDialog(response.msg ?? '登录失败，请重试');
      }
    } catch (e) {
      print('登录错误: $e');
      _showErrorDialog('网络错误，请检查网络连接后重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 保存token到本地存储
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_token', token);
    // 更新全局token
    await _updateGlobalToken(token);
  }

  // 更新全局token
  Future<void> _updateGlobalToken(String newToken) async {
    // 更新SDK中的全局token
    http.updateToken(newToken);
  }

  // 邮箱格式验证
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 显示成功对话框
  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('成功'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF2D3748)),
          onPressed: () {
            print('返回按钮被点击');
            print('Navigator.canPop: ${Navigator.canPop(context)}');
            if (Navigator.canPop(context)) {
              Navigator.of(context).pop();
            } else {
              print('无法返回，导航栈为空');
              // 如果无法返回，跳转到主页
              Navigator.of(context).pushReplacementNamed('/home');
            }
          },
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFD3E8E7),
              Color(0xFFF5F5F5),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Container(
              height: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  // 上部分 - LOGO和欢迎文字 (1/3)
                  Expanded(
                    flex: 3,
                    child: AuthHeader(
                      welcomeText: 'Hi\n欢迎加入\nFoxyra',
                      initialLanguage: _selectedLanguage,
                      languages: _languages,
                      onLanguageChanged: (String newLanguage) {
                        setState(() {
                          _selectedLanguage = newLanguage;
                        });
                      },
                    ),
                  ),
                  
                  // 中间部分 - 输入框和链接
                  Expanded(
                    flex: 3,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 邮箱输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              hintText: '请输入邮箱',
                              prefixIcon: const Icon(
                                Icons.email_outlined,
                                color: Color(0xFF6366F1),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.transparent,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // 密码输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            decoration: InputDecoration(
                              hintText: '请输入密码',
                              prefixIcon: const Icon(
                                Icons.lock_outline,
                                color: Color(0xFF6366F1),
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_off_outlined
                                      : Icons.visibility_outlined,
                                  color: Color(0xFF6366F1),
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.transparent,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // 忘记密码和新用户注册
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                // TODO: 跳转到忘记密码页面
                                print('忘记密码');
                              },
                              child: const Text(
                                '忘记密码?',
                                style: TextStyle(
                                  color: Color(0xFF6366F1),
                                  fontSize: 14,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // TODO: 跳转到注册页面
                                print('新用户注册');
                              },
                              child: const Text(
                                '新用户注册',
                                style: TextStyle(
                                  color: Color(0xFF6366F1),
                                  fontSize: 14,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // 底部 - 登录按钮和协议
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 登录按钮
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isLoading 
                                  ? Colors.grey 
                                  : const Color(0xFF6366F1),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 3,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Text(
                                    '登录',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // 用户协议和隐私政策
                        Wrap(
                          alignment: WrapAlignment.center,
                          children: [
                            const Text(
                              '登录即表示同意 ',
                              style: TextStyle(
                                color: Color(0xFF718096),
                                fontSize: 12,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // TODO: 跳转到用户协议页面
                                print('用户协议');
                              },
                              child: const Text(
                                '用户协议',
                                style: TextStyle(
                                  color: Color(0xFF6366F1),
                                  fontSize: 12,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            const Text(
                              ' 和 ',
                              style: TextStyle(
                                color: Color(0xFF718096),
                                fontSize: 12,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // TODO: 跳转到隐私政策页面
                                print('隐私政策');
                              },
                              child: const Text(
                                '隐私政策',
                                style: TextStyle(
                                  color: Color(0xFF6366F1),
                                  fontSize: 12,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        
                        // 没有账号，去注册
                        TextButton(
                          onPressed: _goToRegister,
                          child: const Text(
                            '没有账号？去注册',
                            style: TextStyle(
                              color: Color(0xFF6366F1),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 跳转到注册页面
  void _goToRegister() {
    Navigator.pushReplacementNamed(context, '/register');
  }
}