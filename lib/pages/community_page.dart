import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../utils/navigation_utils.dart';
import '../sdk/index.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> {
  final ScrollController _scrollController = ScrollController();
  final List<CommunityItem> _items = [];
  bool _isLoading = false;
  bool _hasMoreData = true;
  bool _hasError = false;
  int _currentPage = 1;
  static const int _pageSize = 10;
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 监听滚动事件
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  // 加载初始数据
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentPage = 1;
    });

    try {
      final response = await _apiService.community.getAnnounceList(
        currentPage: 1,
        pageSize: _pageSize,
      );

      if (response.isSuccess && response.data != null) {
        final List<CommunityItem> newItems = response.data!.records
            .map((announce) => _convertToItem(announce))
            .toList();

        setState(() {
          _items.clear();
          _items.addAll(newItems);
          _hasMoreData = newItems.length >= _pageSize;
          _hasError = false;
        });
      } else {
        setState(() {
          _items.clear();
          _hasError = true;
          _hasMoreData = false;
        });
      }
    } catch (e) {
      setState(() {
        _items.clear();
        _hasError = true;
        _hasMoreData = false;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    try {
      final response = await _apiService.community.getAnnounceList(
        currentPage: 1,
        pageSize: _pageSize,
      );

      if (response.isSuccess && response.data != null) {
        final List<CommunityItem> newItems = response.data!.records
            .map((announce) => _convertToItem(announce))
            .toList();

        setState(() {
          _currentPage = 1;
          _items.clear();
          _items.addAll(newItems);
          _hasMoreData = newItems.length >= _pageSize;
          _hasError = false;
        });
      } else {
        setState(() {
          _hasError = true;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
      });
    }
  }

  // 加载更多数据
  Future<void> _loadMoreData() async {
    if (_isLoading || _hasError) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _apiService.community.getAnnounceList(
        currentPage: _currentPage + 1,
        pageSize: _pageSize,
      );

      if (response.isSuccess && response.data != null) {
        final List<CommunityItem> newItems = response.data!.records
            .map((announce) => _convertToItem(announce))
            .toList();

        setState(() {
          if (newItems.isNotEmpty) {
            _items.addAll(newItems);
            _currentPage++;
            _hasMoreData = newItems.length >= _pageSize;
          } else {
            _hasMoreData = false;
          }
        });
      } else {
        setState(() {
          _hasMoreData = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasMoreData = false;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }


  // 将SellAnnounce转换为CommunityItem
  CommunityItem _convertToItem(SellAnnounce announce) {
    // 计算图片高度，基于宽高比或使用默认值
    double imageHeight = 200.0; // 默认高度
    if (announce.w != null && announce.h != null && announce.w! > 0) {
      // 基于原始宽高比计算适合的高度，容器宽度约为339px
      final aspectRatio = announce.h! / announce.w!;
      imageHeight = (339 * aspectRatio).clamp(150.0, 400.0);
    }

    return CommunityItem(
      id: announce.id?.toString() ?? '',
      imageUrl: announce.thumb ?? 'https://picsum.photos/339/400',
      title: announce.name ?? '无标题',
      userAvatar: announce.sellerAvatar ?? 'https://picsum.photos/40/40',
      userName: announce.sellerName ?? '匿名用户',
      imageHeight: imageHeight,
      likeCount: announce.likeTimes ?? 0,
      isLiked: announce.isLike ?? false,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '社区',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        foregroundColor: Colors.black87,
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: _buildBody(),
      ),
    );
  }

  // 构建主体内容
  Widget _buildBody() {
    // 初始加载中
    if (_items.isEmpty && _isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // 网络错误状态
    if (_items.isEmpty && _hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.wifi_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '网络开小差了',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请检查网络连接后重试',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadInitialData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('重新加载'),
            ),
          ],
        ),
      );
    }

    // 数据为空状态
    if (_items.isEmpty && !_isLoading && !_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.explore_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '世界那么大，有你才精彩',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '快来分享你的精彩瞬间吧',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    // 有数据时显示列表
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverMasonryGrid.count(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childCount: _items.length,
            itemBuilder: (context, index) {
              return CommunityCard(
                item: _items[index],
                onLike: () => _toggleLike(index),
              );
            },
          ),
          // 加载更多指示器
          if (_isLoading && _items.isNotEmpty)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          // 没有更多数据提示
          if (!_hasMoreData && _items.isNotEmpty)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: Text(
                    '没有更多内容了',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          // 底部安全区域
          const SliverToBoxAdapter(
            child: SizedBox(height: 20),
          ),
        ],
      ),
    );
  }

  void _toggleLike(int index) async {
    final item = _items[index];
    final announceId = int.tryParse(item.id);
    
    if (announceId == null) {
      return;
    }

    // 先更新UI，提供即时反馈
    setState(() {
      _items[index].isLiked = !_items[index].isLiked;
      if (_items[index].isLiked) {
        _items[index].likeCount++;
      } else {
        _items[index].likeCount--;
      }
    });

    try {
      final response = await _apiService.community.likeAnnounce(
        announceId: announceId,
      );

      if (!response.isSuccess) {
        // 如果API调用失败，回滚UI状态
        setState(() {
          _items[index].isLiked = !_items[index].isLiked;
          if (_items[index].isLiked) {
            _items[index].likeCount++;
          } else {
            _items[index].likeCount--;
          }
        });
      }
    } catch (e) {
      // 网络错误时回滚UI状态
      setState(() {
        _items[index].isLiked = !_items[index].isLiked;
        if (_items[index].isLiked) {
          _items[index].likeCount++;
        } else {
          _items[index].likeCount--;
        }
      });
    }
  }
}

class CommunityCard extends StatelessWidget {
  final CommunityItem item;
  final VoidCallback onLike;

  const CommunityCard({
    super.key,
    required this.item,
    required this.onLike,
  });

  void _onCardTap(BuildContext context) {
    // 使用导航工具类跳转到社区详情页
    NavigationUtils.navigateToCommunityDetail(context, item.id);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _onCardTap(context),
      child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图片部分
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            child: Container(
              width: double.infinity,
              height: item.imageHeight, // 使用预设的图片高度
              color: const Color(0xFFD9DAD4),
              child: Stack(
                children: [
                  // 占位符背景
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: const Color(0xFFD9DAD4),
                    child: const Center(
                      child: Icon(
                        Icons.image_outlined,
                        size: 40,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  // 实际图片
                  Image.network(
                    item.imageUrl,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: const Color(0xFFD9DAD4),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 40,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                '图片加载失败',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) {
                        // 图片加载完成，显示图片
                        return child;
                      }
                      // 图片加载中，显示占位符和进度
                      return Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: const Color(0xFFD9DAD4),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.grey[600]!,
                                  ),
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                '加载中...',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          
          // 内容部分
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 12),
                
                // 底部用户信息和点赞
                Row(
                  children: [
                    // 用户头像
                    ClipOval(
                      child: Container(
                        width: 24,
                        height: 24,
                        color: Colors.grey[300],
                        child: Image.network(
                          item.userAvatar,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.grey,
                            );
                          },
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // 用户昵称
                    Expanded(
                      child: Text(
                        item.userName,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    // 点赞按钮和数量
                    GestureDetector(
                      onTap: onLike,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            item.isLiked ? Icons.favorite : Icons.favorite_border,
                            size: 16,
                            color: item.isLiked ? Colors.red : Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            item.likeCount.toString(),
                            style: TextStyle(
                              fontSize: 12,
                              color: item.isLiked ? Colors.red : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      ),
    );
  }
}

class CommunityItem {
  final String id;
  final String imageUrl;
  final String title;
  final String userAvatar;
  final String userName;
  final double imageHeight; // 预设的图片高度
  int likeCount;
  bool isLiked;

  CommunityItem({
    required this.id,
    required this.imageUrl,
    required this.title,
    required this.userAvatar,
    required this.userName,
    required this.imageHeight,
    required this.likeCount,
    required this.isLiked,
  });
}