import 'package:flutter/material.dart';
import 'home_page.dart';
import 'shop_page.dart';
import 'community_page.dart';
import 'chat_page.dart';
import 'profile_page.dart';

class MainTabPage extends StatefulWidget {
  const MainTabPage({super.key});

  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage> {
  int _currentIndex = 0;
  
  // 用于缓存已创建的页面
  final Map<int, Widget> _cachedPages = {};

  // 页面工厂函数，用于懒加载创建页面
  Widget _createPage(int index) {
    switch (index) {
      case 0:
        return const HomePage();
      case 1:
        return const ShopPage();
      case 2:
        return const CommunityPage();
      case 3:
        return const ChatPage();
      case 4:
        return const ProfilePage();
      default:
        return const HomePage();
    }
  }

  // 获取页面，如果缓存中没有则创建并缓存
  Widget _getPage(int index) {
    if (!_cachedPages.containsKey(index)) {
      _cachedPages[index] = _createPage(index);
    }
    return _cachedPages[index]!;
  }

  @override
  void initState() {
    super.initState();
    // 预加载首页（索引0），因为用户一定会看到
    _cachedPages[0] = _createPage(0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: List.generate(5, (index) {
          // 只为当前页面和已缓存的页面创建Widget
          if (index == _currentIndex || _cachedPages.containsKey(index)) {
            return _getPage(index);
          } else {
            // 对于未访问的页面，返回空容器占位
            return Container();
          }
        }),
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: '主页',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: '商店',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: '社区',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat),
            label: '聊天',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '我的',
          ),
        ],
      ),
    );
  }
}