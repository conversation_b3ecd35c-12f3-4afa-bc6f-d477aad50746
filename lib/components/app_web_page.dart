import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:xk_app/sdk/index.dart';
import 'dart:convert';
import '../sdk/http.dart' as sdk_http;
import '../services/web_resource_interceptor.dart';

class AppWebPage extends StatefulWidget {
  final String url;
  final String? title;
  final bool showAppBar;
  final bool enableJavaScript;
  final bool enableDomStorage;
  final bool enableResourceInterception;
  final Function(String)? onPageStarted;
  final Function(String)? onPageFinished;
  final Function(String)? onWebResourceError;
  final Function(String, bool)? onResourceIntercepted;

  const AppWebPage({
    super.key,
    required this.url,
    this.title,
    this.showAppBar = true,
    this.enableJavaScript = true,
    this.enableDomStorage = true,
    this.enableResourceInterception = true,
    this.onPageStarted,
    this.onPageFinished,
    this.onWebResourceError,
    this.onResourceIntercepted,
  });

  @override
  State<AppWebPage> createState() => _AppWebPageState();
}

class _AppWebPageState extends State<AppWebPage> {
  InAppWebViewController? _webViewController;
  bool _isLoading = true;
  String _currentTitle = '';
  String _currentUrl = '';
  double _loadingProgress = 0.0;
  int _interceptedCount = 0;

  @override
  void initState() {
    super.initState();
    _currentUrl = widget.url;
    _currentTitle = widget.title ?? '';
    _setupCookies();
  }

  Future<void> _setupCookies() async {
    try {
      final cookieManager = CookieManager.instance();
      
      // 清除现有cookies（可选）
      // await cookieManager.deleteAllCookies();
      
      // 注入token cookie到指定域名
      final token = sdk_http.token;
      if (token.isNotEmpty) {
        // 为开发环境域名设置cookie
        await cookieManager.setCookie(
          url: WebUri("http://*************:5173/"),
          name: "_token",
          value: token,
          domain: "*************",
          path: "/",
          isSecure: false,
          isHttpOnly: false,
          sameSite: HTTPCookieSameSitePolicy.LAX,
        );
        
        await cookieManager.setCookie(
          url: WebUri("http://*************:8000/"),
          name: "_token", 
          value: token,
          domain: "*************",
          path: "/",
          isSecure: false,
          isHttpOnly: false,
          sameSite: HTTPCookieSameSitePolicy.LAX,
        );
        
        // 为当前URL的域名也设置cookie
        try {
          final uri = Uri.parse(_currentUrl);
          if (uri.host.isNotEmpty) {
            await cookieManager.setCookie(
              url: WebUri(_currentUrl),
              name: "_token",
              value: token,
              domain: uri.host,
              path: "/",
              isSecure: uri.scheme == 'https',
              isHttpOnly: false,
              sameSite: HTTPCookieSameSitePolicy.LAX,
            );
          }
        } catch (e) {
          debugPrint('为当前URL设置cookie失败: $e');
        }
        
        debugPrint('✅ Cookie设置成功，token: $token');
      }
    } catch (e) {
      debugPrint('❌ Cookie设置失败: $e');
    }
  }

  InAppWebViewSettings get _webViewSettings => InAppWebViewSettings(
    javaScriptEnabled: widget.enableJavaScript,
    domStorageEnabled: widget.enableDomStorage,
    allowsInlineMediaPlayback: true,
    mediaPlaybackRequiresUserGesture: false,
    useOnDownloadStart: true,
    useOnLoadResource: widget.enableResourceInterception,
    useShouldInterceptRequest: widget.enableResourceInterception,
    // clearCache: false,
    clearCache: true,
    cacheEnabled: true,
    supportZoom: true,
    builtInZoomControls: false,
    displayZoomControls: false,
    allowsBackForwardNavigationGestures: true,
    allowsLinkPreview: true,
    isFraudulentWebsiteWarningEnabled: true,
    disallowOverScroll: false,
    allowsAirPlayForMediaPlayback: true,
    allowsPictureInPictureMediaPlayback: true,
    applicationNameForUserAgent: 'XKApp',
    allowFileAccess: true,
    allowFileAccessFromFileURLs: true,
    allowBackgroundAudioPlaying: true,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar
          ? AppBar(
              title: Text(_currentTitle.isNotEmpty ? _currentTitle : 'WebView'),
              backgroundColor: Theme.of(context).colorScheme.inversePrimary,
              actions: [
                if (_isLoading)
                  Container(
                    margin: const EdgeInsets.only(right: 16),
                    width: 20,
                    height: 20,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _webViewController?.reload(),
                ),
                PopupMenuButton<String>(
                  onSelected: _handleMenuAction,
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem(
                      value: 'refresh',
                      child: Row(
                        children: [
                          Icon(Icons.refresh),
                          SizedBox(width: 8),
                          Text('刷新'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'back',
                      child: Row(
                        children: [
                          Icon(Icons.arrow_back),
                          SizedBox(width: 8),
                          Text('后退'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'forward',
                      child: Row(
                        children: [
                          Icon(Icons.arrow_forward),
                          SizedBox(width: 8),
                          Text('前进'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'copy_url',
                      child: Row(
                        children: [
                          Icon(Icons.copy),
                          SizedBox(width: 8),
                          Text('复制链接'),
                        ],
                      ),
                    ),
                    if (widget.enableResourceInterception)
                      PopupMenuItem(
                        value: 'intercepted_info',
                        child: Row(
                          children: [
                            const Icon(Icons.info),
                            const SizedBox(width: 8),
                            Text('已拦截: $_interceptedCount'),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            )
          : null,
      body: Column(
        children: [
          if (_isLoading)
            LinearProgressIndicator(
              value: _loadingProgress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          Expanded(
            child: InAppWebView(
              initialUrlRequest: URLRequest(url: WebUri(_currentUrl)),
              initialSettings: _webViewSettings,
              onWebViewCreated: (controller) {
                _webViewController = controller;
                _setupJavaScriptHandlers();
              },
              onLoadStart: (controller, url) {
                setState(() {
                  _isLoading = true;
                  _loadingProgress = 0.0;
                  _currentUrl = url?.toString() ?? '';
                });
                widget.onPageStarted?.call(_currentUrl);
              },
              onLoadStop: (controller, url) async {
                setState(() {
                  _isLoading = false;
                  _loadingProgress = 1.0;
                });
                
                // 获取页面标题
                final title = await controller.getTitle();
                if (title != null && title.isNotEmpty) {
                  setState(() {
                    _currentTitle = title;
                  });
                }
                
                widget.onPageFinished?.call(url?.toString() ?? '');
              },
              onProgressChanged: (controller, progress) {
                setState(() {
                  _loadingProgress = progress / 100.0;
                });
              },
              onReceivedError: (controller, request, error) {
                setState(() {
                  _isLoading = false;
                });
                widget.onWebResourceError?.call(error.description);
              },
              onLoadResource: widget.enableResourceInterception
                  ? (controller, resource) {
                      _handleResourceLoad(resource);
                    }
                  : null,
              shouldInterceptRequest: widget.enableResourceInterception
                  ? (controller, request) async {
                      return await _interceptRequest(request);
                    }
                  : null,
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint('Console: ${consoleMessage.message}');
              },
            ),
          ),
        ],
      ),
    );
  }

  void _setupJavaScriptHandlers() {
    _webViewController?.addJavaScriptHandler(
      handlerName: 'flutterHandler',
      callback: (args) {
        // 处理来自JavaScript的消息
        debugPrint('JavaScript message: $args');
        return {'status': 'received'};
      },
    );

    // 添加获取token的处理器
    _webViewController?.addJavaScriptHandler(
      handlerName: 'getToken',
      callback: (args) {
        return sdk_http.token;
      },
    );

    // 添加获取用户信息的处理器
    _webViewController?.addJavaScriptHandler(
      handlerName: 'getUserInfo',
      callback: (args) {
        // 这里可以返回用户信息
        return {
          'token': sdk_http.token,
          'lang': sdk_http.globalLang,
          'platform': 'chingga',
          'client': 'APP'
        };
      },
    );

    // 注入SDK相关的JavaScript
    _injectSDKJavaScript();
    
    // 注入token拦截器
    _injectTokenInterceptor();
  }

  void _injectSDKJavaScript() {
    const jsCode = '''
      window.XKApp = {
        version: '1.0.0',
        platform: 'flutter',
        callNative: function(method, params) {
          return window.flutter_inappwebview.callHandler('flutterHandler', {
            method: method,
            params: params
          });
        },
        // SDK相关方法
        getToken: function() {
          return window.flutter_inappwebview.callHandler('getToken');
        },
        getUserInfo: function() {
          return window.flutter_inappwebview.callHandler('getUserInfo');
        }
      };
      
      console.log('✅ XKApp SDK 已注入');
    ''';

    _webViewController?.evaluateJavascript(source: jsCode);
  }

  /// 注入 JavaScript 代码来拦截 fetch 和 XMLHttpRequest 请求
  Future<void> _injectTokenInterceptor() async {
    try {
      // 获取当前的 token
      final token = sdk_http.token;
      
      // 注入 JavaScript 代码
      final jsCode = '''
        (function() {
          // 保存原始的 fetch 函数
          const originalFetch = window.fetch;
          
          // 重写 fetch 函数
          window.fetch = function(url, options = {}) {
            // 检查是否是 API 请求
            if (typeof url === 'string' && url.includes('/api/')) {
              // 确保 headers 对象存在
              options.headers = options.headers || {};
              
              // 添加 token 请求头
              if ('$token' && '$token'.trim() !== '') {
                options.headers['token'] = '$token';
                options.headers['client'] = 'APP';
                options.headers['Accept-Language'] = '${sdk_http.globalLang}';
                options.headers['platform'] = 'chingga';
              }
              
              console.log('拦截到 API 请求:', url, 'Token:', '$token');
            }
            
            // 调用原始的 fetch 函数
            return originalFetch.call(this, url, options);
          };
          
          // 保存原始的 XMLHttpRequest
          const OriginalXHR = window.XMLHttpRequest;
          
          // 重写 XMLHttpRequest
          window.XMLHttpRequest = function() {
            const xhr = new OriginalXHR();
            const originalOpen = xhr.open;
            const originalSetRequestHeader = xhr.setRequestHeader;
            
            let isApiRequest = false;
            let requestUrl = '';
            
            // 重写 open 方法
            xhr.open = function(method, url, async, user, password) {
              requestUrl = url;
              isApiRequest = typeof url === 'string' && url.includes('/api/');
              return originalOpen.call(this, method, url, async, user, password);
            };
            
            // 重写 setRequestHeader 方法
            xhr.setRequestHeader = function(name, value) {
              return originalSetRequestHeader.call(this, name, value);
            };
            
            // 重写 send 方法
            const originalSend = xhr.send;
            xhr.send = function(data) {
              // 如果是 API 请求，添加 token 请求头
              if (isApiRequest && '$token' && '$token'.trim() !== '') {
                this.setRequestHeader('token', '$token');
                this.setRequestHeader('client', 'APP');
                this.setRequestHeader('Accept-Language', '${sdk_http.globalLang}');
                this.setRequestHeader('platform', 'chingga');
                console.log('拦截到 XMLHttpRequest API 请求:', requestUrl, 'Token:', '$token');
              }
              
              return originalSend.call(this, data);
            };
            
            return xhr;
          };
          
          // 复制原始 XMLHttpRequest 的属性
          for (let prop in OriginalXHR) {
            if (OriginalXHR.hasOwnProperty(prop)) {
              window.XMLHttpRequest[prop] = OriginalXHR[prop];
            }
          }
          
          console.log('✅ Token 拦截器已注入，当前 token:', '$token');
        })();
      ''';
      
      await _webViewController?.evaluateJavascript(source: jsCode);
      debugPrint('✅ Token 拦截器注入成功，当前 token: $token');
    } catch (e) {
      debugPrint('❌ Token 拦截器注入失败: $e');
    }
  }

  void _handleResourceLoad(LoadedResource resource) {
    if (widget.enableResourceInterception) {
      final url = resource.url?.toString() ?? '';
      debugPrint('Loading resource: $url');
    }
  }

  Future<WebResourceResponse?> _interceptRequest(WebResourceRequest request) async {
    if (!widget.enableResourceInterception) return null;

    final url = request.url.toString();
    debugPrint('Intercepting request: $url');

    try {
      // 使用WebResourceInterceptor的静态方法处理请求
      final shouldIntercept = WebResourceInterceptor.shouldIntercept(url);

      if (shouldIntercept) {
        setState(() {
          _interceptedCount++;
        });

        // 获取本地资源数据
        final assetBytes = await WebResourceInterceptor.getLocalAssetBytes(url);
        widget.onResourceIntercepted?.call(url, true);
        
        // 记录拦截日志
        WebResourceInterceptor.logInterception(url, true);

        if (assetBytes != null) {
          return WebResourceResponse(
            contentType: _getMimeType(url),
            data: assetBytes,
            headers: {
              'Content-Type': '${_getMimeType(url)}; charset=utf-8',
              'Cache-Control': 'max-age=31536000',
              'Access-Control-Allow-Origin': '*',
            },
            statusCode: 200,
          );
        }
      }
    } catch (e) {
      debugPrint('Error intercepting request: $e');
      widget.onResourceIntercepted?.call(url, false);
      WebResourceInterceptor.logInterception(url, false);
    }

    return null;
  }

  // 根据URL获取MIME类型
  String _getMimeType(String url) {
    if (url.endsWith('.js')) {
      return 'application/javascript';
    } else if (url.endsWith('.css')) {
      return 'text/css';
    } else if (url.endsWith('.json')) {
      return 'application/json';
    } else if (url.endsWith('.html')) {
      return 'text/html';
    }
    return 'text/plain';
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'refresh':
        _webViewController?.reload();
        break;
      case 'back':
        _webViewController?.goBack();
        break;
      case 'forward':
        _webViewController?.goForward();
        break;
      case 'copy_url':
        _copyCurrentUrl();
        break;
      case 'intercepted_info':
        _showInterceptedInfo();
        break;
    }
  }

  void _copyCurrentUrl() {
    // 复制当前URL到剪贴板
    // 这里可以使用clipboard插件
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已复制: $_currentUrl')),
    );
  }

  void _showInterceptedInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('资源拦截信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('当前页面: $_currentUrl'),
            const SizedBox(height: 8),
            Text('已拦截请求数: $_interceptedCount'),
            const SizedBox(height: 8),
            Text('拦截状态: ${widget.enableResourceInterception ? "启用" : "禁用"}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _webViewController = null;
    super.dispose();
  }
}