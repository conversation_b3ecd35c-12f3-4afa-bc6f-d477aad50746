import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xk_app/sdk/index.dart';
import 'dart:convert';
import 'dart:typed_data';
import '../sdk/http.dart' as sdk_http;
import '../services/web_resource_interceptor.dart';
// #docregion platform_imports
// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS features.
// import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
// #enddocregion platform_imports

class WebPage extends StatefulWidget {
  final String url;
  final String? title;
  final bool showAppBar;
  final bool enableJavaScript;
  final bool enableDomStorage;
  final bool enableResourceInterception;
  final Function(String)? onPageStarted;
  final Function(String)? onPageFinished;
  final Function(WebResourceError)? onWebResourceError;
  final Function(String, bool)? onResourceIntercepted;

  const WebPage({
    super.key,
    required this.url,
    this.title,
    this.showAppBar = true,
    this.enableJavaScript = true,
    this.enableDomStorage = true,
    this.enableResourceInterception = true,
    this.onPageStarted,
    this.onPageFinished,
    this.onWebResourceError,
    this.onResourceIntercepted,
  });

  @override
  State<WebPage> createState() => _WebPageState();
}

class _WebPageState extends State<WebPage> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String _currentTitle = '';
  String _currentUrl = '';
  double _loadingProgress = 0.0;
  final int _interceptedCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializePlatform() {
    // #docregion platform_features
    late final PlatformWebViewControllerCreationParams params;
    
    // 根据平台创建不同的参数
    if (defaultTargetPlatform == TargetPlatform.android) {
      params = AndroidWebViewControllerCreationParams();
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewCookieManager cookieManager = WebViewCookieManager();

    cookieManager.clearCookies();

    // cookieManager.setCookie(WebViewCookie(name: "token", value: token, domain: ""));
    cookieManager.setCookie(WebViewCookie(name: "_token", value: token, domain: "http://*************:5173/"));
    cookieManager.setCookie(WebViewCookie(name: "_token", value: token, domain: "http://*************:8000/"));

    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);
    // #enddocregion platform_features

    _controller = controller;
  }

  void _initializeWebView() {
    // 如果是Web平台，显示提示信息
    if (kIsWeb) {
      return;
    }
    
    _initializePlatform();
    _controller
      ..setJavaScriptMode(
        widget.enableJavaScript 
          ? JavaScriptMode.unrestricted 
          : JavaScriptMode.disabled
      )
      ..setBackgroundColor(const Color(0x00000000));

    // 如果启用了资源拦截，设置资源拦截器
    if (widget.enableResourceInterception) {
      _setupResourceInterception();
    }

    _controller.loadRequest(Uri.parse(widget.url));
  }

  Future<void> _updateTitle() async {
    try {
      final title = await _controller.getTitle();
      if (title != null && title.isNotEmpty && mounted) {
        setState(() {
          _currentTitle = title;
        });
      }
    } catch (e) {
      // 获取标题失败时忽略错误
      debugPrint('获取标题失败: $e');
    }
  }

  String _getDomainFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return url.length > 50 ? '${url.substring(0, 50)}...' : url;
    }
  }

  void _setupResourceInterception() {
    debugPrint('🔄 设置JavaScript资源拦截方案');
    // 由于webview_flutter的资源拦截API在不同版本中有变化
    // 我们使用JavaScript注入的方式来实现资源拦截
    _setupJavaScriptInterception();
  }
  
  // JavaScript拦截方案 - 通过重写import和fetch来实现
  void _setupJavaScriptInterception() {
    // 在页面加载完成后注入拦截脚本
    _controller.setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          setState(() {
            _loadingProgress = progress / 100.0;
          });
        },
        onPageStarted: (String url) {
          setState(() {
            _isLoading = true;
            _currentUrl = url;
          });
          widget.onPageStarted?.call(url);
        },
        onPageFinished: (String url) {
          setState(() {
            _isLoading = false;
            _currentUrl = url;
          });
          _updateTitle();
          
          // 注入资源拦截JavaScript
          // _injectResourceInterceptor();
          
          // 模拟拦截成功的回调用于测试
          // Future.delayed(const Duration(milliseconds: 1000), () {
          //   if (url.contains('vue') || url.contains('test')) {
          //     widget.onResourceIntercepted?.call('https://unpkg.com/vue@3.5.17/dist/vue.esm-browser.prod.js', true);
          //   }
          // });
          
          // 延迟再次更新标题，处理动态加载的内容
          Future.delayed(const Duration(milliseconds: 500), () {
            _updateTitle();
          });
          widget.onPageFinished?.call(url);
        },
        onWebResourceError: (WebResourceError error) {
          setState(() {
            _isLoading = false;
          });
          widget.onWebResourceError?.call(error);
          _showErrorSnackBar(error.description);
        },
        onNavigationRequest: (NavigationRequest request) {
          return NavigationDecision.navigate;
        },
        onUrlChange: (UrlChange change) {
          if (change.url != null) {
            setState(() {
              _currentUrl = change.url!;
            });
            Future.delayed(const Duration(milliseconds: 300), () {
              _updateTitle();
            });
            Future.delayed(const Duration(milliseconds: 1000), () {
              _updateTitle();
            });
          }
        },
      ),
    );
  }
  
  // 注入资源拦截JavaScript代码
  Future<void> _injectResourceInterceptor() async {
    try {
      // 首先设置JavaScript通信桥接
      _controller.addJavaScriptChannel(
        'FlutterResourceInterceptor',
        onMessageReceived: (JavaScriptMessage message) {
          try {
            final data = jsonDecode(message.message);
            final url = data['url'] as String;
            final success = data['success'] as bool;
            
            debugPrint('📨 收到JavaScript拦截消息: $url, 成功: $success');
            widget.onResourceIntercepted?.call(url, success);
            
            if (success) {
              WebResourceInterceptor.logInterception(url, true);
            }
          } catch (e) {
            debugPrint('❌ 解析JavaScript消息失败: $e');
          }
        },
      );
      
      // 获取完整的本地Vue.js内容并进行Base64编码避免转义问题
      final vueContent = await WebResourceInterceptor.loadLocalAsset('assets/js/vue.esm-browser.prod.js');
      final vueBytes = utf8.encode(vueContent);
      final base64VueContent = base64Encode(vueBytes);
      
      final jsCode = '''
        (function() {
          console.log('🚀 开始注入资源拦截器');
          
          // 拦截的URL列表
          const interceptUrls = [
            'https://unpkg.com/vue@3.5.17/dist/vue.esm-browser.prod.js',
            'https://unpkg.com/vue@3/dist/vue.esm-browser.prod.js',
            'https://cdn.jsdelivr.net/npm/vue@3.5.17/dist/vue.esm-browser.prod.js'
          ];
          
          // 本地Vue.js内容（Base64编码）
          const base64VueContent = '$base64VueContent';
          const localVueContent = atob(base64VueContent);
          
          // 通知Flutter拦截成功的函数
          function notifyFlutter(url, success) {
            try {
              FlutterResourceInterceptor.postMessage(JSON.stringify({
                url: url,
                success: success
              }));
            } catch (e) {
              console.log('通知Flutter失败:', e);
            }
          }
          
          // 重写fetch以拦截资源请求
          const originalFetch = window.fetch;
          window.fetch = async function(url, options) {
            if (typeof url === 'string' && interceptUrls.includes(url)) {
              console.log('✅ 拦截fetch请求:', url);
              notifyFlutter(url, true);
              
              // 返回本地Vue.js内容，完全模拟CDN响应
              return new Response(localVueContent, {
                status: 200,
                statusText: 'OK',
                headers: {
                  'Content-Type': 'application/javascript; charset=utf-8',
                  'Cache-Control': 'max-age=31536000',
                  'Access-Control-Allow-Origin': '*',
                  'Content-Length': localVueContent.length.toString()
                }
              });
            }
            
            return originalFetch.call(this, url, options);
          };
          
          // 重写动态import
          const originalImport = window.import;
          if (originalImport) {
            window.import = async function(url) {
              console.log('🔍 拦截import请求:', url);
              
              if (interceptUrls.includes(url)) {
                console.log('✅ 拦截import成功，使用本地资源:', url);
                notifyFlutter(url, true);
                
                // 创建Blob URL来模拟真实的模块加载
                const blob = new Blob([localVueContent], {type: 'application/javascript'});
                const blobUrl = URL.createObjectURL(blob);
                
                try {
                  const module = await originalImport.call(this, blobUrl);
                  URL.revokeObjectURL(blobUrl);
                  return module;
                } catch (e) {
                  URL.revokeObjectURL(blobUrl);
                  console.error('本地模块加载失败:', e);
                  throw e;
                }
              }
              
              return originalImport.call(this, url);
            };
          }
          
          // 重写XMLHttpRequest
          const OriginalXHR = window.XMLHttpRequest;
          window.XMLHttpRequest = function() {
            const xhr = new OriginalXHR();
            const originalOpen = xhr.open;
            const originalSend = xhr.send;
            
            let interceptedUrl = null;
            
            xhr.open = function(method, url, ...args) {
              if (typeof url === 'string' && interceptUrls.includes(url)) {
                interceptedUrl = url;
              }
              return originalOpen.call(this, method, url, ...args);
            };
            
            xhr.send = function(data) {
              if (interceptedUrl) {
                console.log('✅ 拦截XHR请求:', interceptedUrl);
                notifyFlutter(interceptedUrl, true);
                
                // 模拟成功响应
                setTimeout(() => {
                  Object.defineProperty(xhr, 'status', { value: 200, writable: false });
                  Object.defineProperty(xhr, 'statusText', { value: 'OK', writable: false });
                  Object.defineProperty(xhr, 'responseText', { value: localVueContent, writable: false });
                  Object.defineProperty(xhr, 'readyState', { value: 4, writable: false });
                  
                  if (xhr.onreadystatechange) {
                    xhr.onreadystatechange();
                  }
                  if (xhr.onload) {
                    xhr.onload();
                  }
                }, 10);
                
                return;
              }
              
              return originalSend.call(this, data);
            };
            
            return xhr;
          };
          
          // 复制XMLHttpRequest的静态属性
          for (let prop in OriginalXHR) {
            if (OriginalXHR.hasOwnProperty(prop)) {
              window.XMLHttpRequest[prop] = OriginalXHR[prop];
            }
          }
          
          console.log('✅ 资源拦截器注入完成');
        })();
      ''';
      
      await _controller.runJavaScript(jsCode);
      debugPrint('✅ JavaScript资源拦截器注入成功');
      
    } catch (e) {
      debugPrint('❌ 注入JavaScript资源拦截器失败: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载失败: $message'),
          action: SnackBarAction(
            label: '重试',
            onPressed: () => _controller.reload(),
          ),
        ),
      );
    }
  }

  Future<void> _refresh() async {
    await _controller.reload();
  }

  /// 注入 JavaScript 代码来拦截 fetch 和 XMLHttpRequest 请求
  Future<void> _injectTokenInterceptor() async {
    try {
      // 获取当前的 token
      final token = sdk_http.token;
      
      // 注入 JavaScript 代码
      final jsCode = '''
        (function() {
          // 保存原始的 fetch 函数
          const originalFetch = window.fetch;
          
          // 重写 fetch 函数
          window.fetch = function(url, options = {}) {
            // 检查是否是 API 请求
            if (typeof url === 'string' && url.includes('/api/')) {
              // 确保 headers 对象存在
              options.headers = options.headers || {};
              
              // 添加 token 请求头
              if ('$token' && '$token'.trim() !== '') {
                options.headers['token'] = '$token';
                options.headers['client'] = 'APP';
                options.headers['Accept-Language'] = '${sdk_http.globalLang}';
                options.headers['platform'] = 'chingga';
              }
              
              console.log('拦截到 API 请求:', url, 'Token:', '$token');
            }
            
            // 调用原始的 fetch 函数
            return originalFetch.call(this, url, options);
          };
          
          // 保存原始的 XMLHttpRequest
          const OriginalXHR = window.XMLHttpRequest;
          
          // 重写 XMLHttpRequest
          window.XMLHttpRequest = function() {
            const xhr = new OriginalXHR();
            const originalOpen = xhr.open;
            const originalSetRequestHeader = xhr.setRequestHeader;
            
            let isApiRequest = false;
            let requestUrl = '';
            
            // 重写 open 方法
            xhr.open = function(method, url, async, user, password) {
              requestUrl = url;
              isApiRequest = typeof url === 'string' && url.includes('/api/');
              return originalOpen.call(this, method, url, async, user, password);
            };
            
            // 重写 setRequestHeader 方法
            xhr.setRequestHeader = function(name, value) {
              return originalSetRequestHeader.call(this, name, value);
            };
            
            // 重写 send 方法
            const originalSend = xhr.send;
            xhr.send = function(data) {
              // 如果是 API 请求，添加 token 请求头
              if (isApiRequest && '$token' && '$token'.trim() !== '') {
                this.setRequestHeader('token', '$token');
                this.setRequestHeader('client', 'APP');
                this.setRequestHeader('Accept-Language', '${sdk_http.globalLang}');
                this.setRequestHeader('platform', 'chingga');
                console.log('拦截到 XMLHttpRequest API 请求:', requestUrl, 'Token:', '$token');
              }
              
              return originalSend.call(this, data);
            };
            
            return xhr;
          };
          
          // 复制原始 XMLHttpRequest 的属性
          for (let prop in OriginalXHR) {
            if (OriginalXHR.hasOwnProperty(prop)) {
              window.XMLHttpRequest[prop] = OriginalXHR[prop];
            }
          }
          
          console.log('Token 拦截器已注入，当前 token:', '$token');
        })();
      ''';
      
      await _controller.runJavaScript(jsCode);
      debugPrint('✅ Token 拦截器注入成功，当前 token: $token');
    } catch (e) {
      debugPrint('❌ Token 拦截器注入失败: $e');
    }
  }



  @override
  Widget build(BuildContext context) {
    // 如果是Web平台，显示不支持的提示
    if (kIsWeb) {
      return Scaffold(
        appBar: widget.showAppBar
            ? AppBar(
                title: Text(widget.title ?? 'WebView 不支持'),
                backgroundColor: Theme.of(context).colorScheme.inversePrimary,
              )
            : null,
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.web_asset_off,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'WebView 在 Web 平台不支持',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                '请在移动设备或桌面应用中使用',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final canGoBack = await _controller.canGoBack();
        if (canGoBack) {
          await _controller.goBack();
        } else {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: widget.showAppBar
            ? AppBar(
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.title ?? (_currentTitle.isNotEmpty 
                        ? _currentTitle 
                        : '加载中...'),
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (_currentUrl.isNotEmpty && widget.title == null)
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getDomainFromUrl(_currentUrl),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.black54,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (widget.enableResourceInterception && _interceptedCount > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '拦截: $_interceptedCount',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.inversePrimary,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _refresh,
                    tooltip: '刷新',
                  ),
                  // PopupMenuButton<String>(
                  //   onSelected: (value) async {
                  //     switch (value) {
                  //       case 'forward':
                  //         if (await _controller.canGoForward()) {
                  //           await _controller.goForward();
                  //         }
                  //         break;
                  //       case 'reload':
                  //         await _controller.reload();
                  //         break;
                  //       case 'copy_url':
                  //         final url = await _controller.currentUrl();
                  //         if (url != null) {
                  //           // 这里可以添加复制到剪贴板的功能
                  //           ScaffoldMessenger.of(context).showSnackBar(
                  //             SnackBar(content: Text('URL: $url')),
                  //           );
                  //         }
                  //         break;
                  //     }
                  //   },
                  //   itemBuilder: (context) => [
                  //     const PopupMenuItem(
                  //       value: 'forward',
                  //       child: ListTile(
                  //         leading: Icon(Icons.arrow_forward),
                  //         title: Text('前进'),
                  //         contentPadding: EdgeInsets.zero,
                  //       ),
                  //     ),
                  //     const PopupMenuItem(
                  //       value: 'reload',
                  //       child: ListTile(
                  //         leading: Icon(Icons.refresh),
                  //         title: Text('重新加载'),
                  //         contentPadding: EdgeInsets.zero,
                  //       ),
                  //     ),
                  //     const PopupMenuItem(
                  //       value: 'copy_url',
                  //       child: ListTile(
                  //         leading: Icon(Icons.copy),
                  //         title: Text('复制链接'),
                  //         contentPadding: EdgeInsets.zero,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                ],
                bottom: _isLoading
                    ? PreferredSize(
                        preferredSize: const Size.fromHeight(4.0),
                        child: LinearProgressIndicator(
                          value: _loadingProgress,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                      )
                    : null,
              )
            : null,
        body: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (_isLoading && !widget.showAppBar)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: LinearProgressIndicator(
                  value: _loadingProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
          ],
        ),
        floatingActionButton: widget.showAppBar
            ? null
            : FloatingActionButton(
                mini: true,
                onPressed: () => Navigator.of(context).pop(),
                child: const Icon(Icons.close),
              ),
      ),
    );
  }
}