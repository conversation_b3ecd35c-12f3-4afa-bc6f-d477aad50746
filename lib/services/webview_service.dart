
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xk_app/sdk/http.dart' as sdk_http;

/// A service to manage a pool of pre-warmed WebViewController instances.
/// This helps to speed up the initial loading of WebViews.
class WebViewService {
  // Singleton instance
  static final WebViewService _instance = WebViewService._internal();
  factory WebViewService() => _instance;
  WebViewService._internal();

  WebViewController? _cachedController;

  /// Pre-warms a WebViewController instance.
  /// This should be called early in the app's lifecycle.
  Future<void> prewarmController() async {
    if (_cachedController == null) {
      _cachedController = _createController();
      // You can optionally load a blank page or a local lightweight page
      // to fully initialize the native WebView component.
      await _cachedController!.loadRequest(Uri.parse('about:blank'));
      debugPrint('✅ WebView controller pre-warmed.');
    }
  }

  /// Gets a WebViewController.
  /// If a pre-warmed controller is available, it returns it and prepares a new one for the next request.
  /// Otherwise, it creates a new one on-the-fly.
  WebViewController getController() {
    final controller = _cachedController ?? _createController();
    
    // Immediately start creating the next controller for the cache.
    _cachedController = null; // Invalidate the current one
    prewarmController(); // Asynchronously warm up the next one
    
    return controller;
  }

  WebViewController _createController() {
    late final PlatformWebViewControllerCreationParams params;
    params = const PlatformWebViewControllerCreationParams();

    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000));
      
    debugPrint('✅ New WebView controller created.');
    return controller;
  }

  /// Injects the token interceptor JavaScript into the given controller.
  /// This is extracted from the WebPage widget to be reusable.
  static Future<void> injectTokenInterceptor(WebViewController controller) async {
    try {
      final token = sdk_http.token;
      
      final jsCode = '''
        (function() {
          const originalFetch = window.fetch;
          window.fetch = function(url, options = {}) {
            if (typeof url === 'string' && url.includes('/api/')) {
              options.headers = options.headers || {};
              if ('$token' && '$token'.trim() !== '') {
                options.headers['token'] = '$token';
                options.headers['client'] = 'APP';
                options.headers['Accept-Language'] = '${sdk_http.globalLang}';
                options.headers['platform'] = 'chingga';
              }
            }
            return originalFetch.call(this, url, options);
          };
          
          const OriginalXHR = window.XMLHttpRequest;
          function InterceptedXHR() {
            const xhr = new OriginalXHR();
            const originalOpen = xhr.open;
            
            let isApiRequest = false;
            
            xhr.open = function(method, url, ...args) {
              isApiRequest = typeof url === 'string' && url.includes('/api/');
              return originalOpen.call(this, method, url, ...args);
            };
            
            const originalSend = xhr.send;
            xhr.send = function(data) {
              if (isApiRequest && '$token' && '$token'.trim() !== '') {
                this.setRequestHeader('token', '$token');
                this.setRequestHeader('client', 'APP');
                this.setRequestHeader('Accept-Language', '${sdk_http.globalLang}');
                this.setRequestHeader('platform', 'chingga');
              }
              return originalSend.call(this, data);
            };
            
            return xhr;
          }

          // Copy static properties from the original XHR
          for (let prop in OriginalXHR) {
            if (OriginalXHR.hasOwnProperty(prop)) {
              InterceptedXHR[prop] = OriginalXHR[prop];
            }
          }
          
          window.XMLHttpRequest = InterceptedXHR;
          console.log('Token interceptor updated, token: $token');
        })();
      ''';
      
      await controller.runJavaScript(jsCode);
      debugPrint('✅ Token Interceptor injected via service.');
    } catch (e) {
      debugPrint('❌ Failed to inject Token Interceptor via service: $e');
    }
  }
}
