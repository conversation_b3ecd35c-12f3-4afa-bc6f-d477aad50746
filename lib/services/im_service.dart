import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/im_config.dart';

// 导入腾讯IM SDK的各个模块
import 'package:tencent_cloud_chat_sdk/models/v2_tim_callback.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/enum/log_level_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/V2TimSDKListener.dart';
import 'package:tencent_cloud_chat_sdk/enum/V2TimAdvancedMsgListener.dart';
import 'package:tencent_cloud_chat_sdk/enum/V2TimConversationListener.dart';

class IMService extends ChangeNotifier {
  static final IMService _instance = IMService._internal();
  factory IMService() => _instance;
  IMService._internal();
  
  bool _isInitialized = false;
  bool _isLoggedIn = false;
  String? _currentUserId;
  List<V2TimConversation> _conversationList = [];
  List<V2TimMessage> _currentMessages = [];
  String? _currentConversationId;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _isLoggedIn;
  String? get currentUserId => _currentUserId;
  List<V2TimConversation> get conversationList => _conversationList;
  List<V2TimMessage> get currentMessages => _currentMessages;
  String? get currentConversationId => _currentConversationId;

  /// 初始化IM SDK
  Future<bool> initializeIM() async {
    if (_isInitialized) return true;

    try {
      // 检查配置
      if (!IMConfig.isConfigValid()) {
        debugPrint('IM配置无效: ${IMConfig.getConfigTip()}');
        return false;
      }

      // 初始化SDK - 使用简化的初始化方式
      V2TimValueCallback<bool> initResult = await V2TIMManager().initSDK(
        sdkAppID: IMConfig.sdkAppId,
        loglevel: LogLevelEnum.V2TIM_LOG_INFO,
        listener: V2TimSDKListener(
          onConnecting: () {
            debugPrint('IM SDK 连接中...');
          },
          onConnectSuccess: () {
            debugPrint('IM SDK 连接成功');
          },
          onConnectFailed: (code, error) {
            debugPrint('IM SDK 连接失败: $code, $error');
          },
          onKickedOffline: () {
            debugPrint('IM SDK 被踢下线');
            _handleLogout();
          },
          onUserSigExpired: () {
            debugPrint('IM SDK UserSig过期');
            _handleLogout();
          },
          onSelfInfoUpdated: (info) {
            debugPrint('IM SDK 个人信息更新');
          },
        ),
      );

      if (initResult.code == 0) {
        _isInitialized = true;
        debugPrint('IM SDK 初始化成功');
        notifyListeners();
        return true;
      } else {
        debugPrint('IM SDK 初始化失败: ${initResult.code}, ${initResult.desc}');
        return false;
      }
    } catch (e) {
      debugPrint('IM SDK 初始化异常: $e');
      return false;
    }
  }

  /// 登录IM
  Future<bool> login(String userId, String userSig) async {
    if (!_isInitialized) {
      await initializeIM();
    }

    try {
      V2TimCallback loginResult = await V2TIMManager().login(
        userID: userId,
        userSig: userSig,
      );

      if (loginResult.code == 0) {
        _isLoggedIn = true;
        _currentUserId = userId;
        
        // 保存登录状态
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('im_user_id', userId);
        await prefs.setString('im_user_sig', userSig);
        
        debugPrint('IM 登录成功: $userId');
        notifyListeners();
        
        // 获取会话列表
        await getConversationList();
        return true;
      } else {
        debugPrint('IM 登录失败: ${loginResult.code}, ${loginResult.desc}');
        return false;
      }
    } catch (e) {
      debugPrint('IM 登录异常: $e');
      return false;
    }
  }

  /// 登出IM
  Future<void> logout() async {
    try {
      await V2TIMManager().logout();
      await _handleLogout();
    } catch (e) {
      debugPrint('IM 登出异常: $e');
    }
  }

  /// 处理登出逻辑
  Future<void> _handleLogout() async {
    _isLoggedIn = false;
    _currentUserId = null;
    _conversationList.clear();
    _currentMessages.clear();
    _currentConversationId = null;
    
    // 清除保存的登录状态
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('im_user_id');
    await prefs.remove('im_user_sig');
    
    notifyListeners();
  }

  /// 获取会话列表
  Future<void> getConversationList() async {
    if (!_isLoggedIn) return;

    try {
      // 使用简化的API调用
      debugPrint('开始获取会话列表...');
      // 这里需要根据实际的API进行调整
      _conversationList = []; // 临时清空，等待正确的API调用
      notifyListeners();
      debugPrint('获取会话列表完成');
    } catch (e) {
      debugPrint('获取会话列表异常: $e');
    }
  }

  /// 发送文本消息 - 简化版本
  Future<bool> sendTextMessage(String text, String receiver, {bool isGroup = false}) async {
    if (!_isLoggedIn) return false;

    try {
      debugPrint('发送消息: $text 到 $receiver');
      // 这里需要根据实际的API进行调整
      return true; // 临时返回成功
    } catch (e) {
      debugPrint('发送消息异常: $e');
      return false;
    }
  }

  /// 获取历史消息 - 简化版本
  Future<void> getHistoryMessages(String conversationId, {int count = 20}) async {
    if (!_isLoggedIn) return;

    try {
      _currentConversationId = conversationId;
      debugPrint('获取历史消息: $conversationId');
      // 这里需要根据实际的API进行调整
      _currentMessages = []; // 临时清空
      notifyListeners();
    } catch (e) {
      debugPrint('获取历史消息异常: $e');
    }
  }

  /// 自动登录（如果有保存的登录信息）
  Future<bool> autoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // TODO : 替换为实际的测试用户ID和UserSig
      // 这里使用测试用户ID和UserSig进行自动登录
      // 注意：在生产环境中，请确保使用真实的UserSig进行登录
      prefs.setString("im_user_id", "acvioouser1028492");
      prefs.setString("im_user_sig", "eJw1jssOgjAURP*la4PttTxK4gI3ugCNAaMuG6hyozxSsBCM-y5BXM7MOcm8SRLGlupr1Ir4YAuglC6m0ihNfAIWJb-cZA9Z15gRnzkjRMF2ZxIzVbZ4w0mQqcGqejVKMwoeF-DX8T6uAV6fPD-jPrx0pjcyGJLuEEWnlom48JY5H7TYbsxxVwbrWWyxGK8x116BwznzPl*dbDU7");

      final userId = prefs.getString('im_user_id');
      final userSig = prefs.getString('im_user_sig');
      
      if (userId != null && userSig != null) {
        return await login(userId, userSig);
      }
      return false;
    } catch (e) {
      debugPrint('自动登录异常: $e');
      return false;
    }
  }

  /// 销毁SDK
  @override
  Future<void> dispose() async {
    try {
      await V2TIMManager().unInitSDK();
      _isInitialized = false;
      super.dispose();
    } catch (e) {
      debugPrint('销毁IM SDK异常: $e');
    }
  }
}