import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

/// IM用户信息
class IMUser {
  final String userId;
  final String nickname;
  final String? avatar;
  final bool isOnline;

  IMUser({
    required this.userId,
    required this.nickname,
    this.avatar,
    this.isOnline = false,
  });

  factory IMUser.fromJson(Map<String, dynamic> json) {
    return IMUser(
      userId: json['userId'] ?? '',
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'],
      isOnline: json['isOnline'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'nickname': nickname,
      'avatar': avatar,
      'isOnline': isOnline,
    };
  }
}

/// 聊天消息扩展
class ChatMessage {
  final V2TimMessage originalMessage;
  final String displayText;
  final DateTime timestamp;
  final bool isSelf;
  final String senderNickname;
  final String? senderAvatar;

  ChatMessage({
    required this.originalMessage,
    required this.displayText,
    required this.timestamp,
    required this.isSelf,
    required this.senderNickname,
    this.senderAvatar,
  });

  factory ChatMessage.fromV2TimMessage(V2TimMessage message, String currentUserId) {
    String displayText = '';
    
    // 根据消息类型获取显示文本
    switch (message.elemType) {
      case 1: // V2TIM_ELEM_TYPE_TEXT
        displayText = message.textElem?.text ?? '';
        break;
      case 2: // V2TIM_ELEM_TYPE_CUSTOM
        displayText = '[自定义消息]';
        break;
      case 3: // V2TIM_ELEM_TYPE_IMAGE
        displayText = '[图片]';
        break;
      case 4: // V2TIM_ELEM_TYPE_SOUND
        displayText = '[语音]';
        break;
      case 5: // V2TIM_ELEM_TYPE_VIDEO
        displayText = '[视频]';
        break;
      case 6: // V2TIM_ELEM_TYPE_FILE
        displayText = '[文件]';
        break;
      case 7: // V2TIM_ELEM_TYPE_LOCATION
        displayText = '[位置]';
        break;
      case 8: // V2TIM_ELEM_TYPE_FACE
        displayText = '[表情]';
        break;
      case 9: // V2TIM_ELEM_TYPE_GROUP_TIPS
        displayText = '[群提示消息]';
        break;
      default:
        displayText = '[未知消息]';
    }

    return ChatMessage(
      originalMessage: message,
      displayText: displayText,
      timestamp: DateTime.fromMillisecondsSinceEpoch((message.timestamp ?? 0) * 1000),
      isSelf: message.sender == currentUserId,
      senderNickname: message.nickName ?? message.sender ?? '',
      senderAvatar: message.faceUrl,
    );
  }
}

/// 会话信息扩展
class ChatConversation {
  final V2TimConversation originalConversation;
  final String displayName;
  final String? avatar;
  final String lastMessageText;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isGroup;

  ChatConversation({
    required this.originalConversation,
    required this.displayName,
    this.avatar,
    required this.lastMessageText,
    required this.lastMessageTime,
    required this.unreadCount,
    required this.isGroup,
  });

  factory ChatConversation.fromV2TimConversation(V2TimConversation conversation) {
    String displayName = '';
    String? avatar;
    bool isGroup = false;

    if (conversation.type == 1) { // V2TIM_C2C
      // 单聊
      displayName = conversation.showName ?? conversation.userID ?? '';
      avatar = conversation.faceUrl;
      isGroup = false;
    } else if (conversation.type == 2) { // V2TIM_GROUP
      // 群聊
      displayName = conversation.showName ?? conversation.groupID ?? '';
      avatar = conversation.faceUrl;
      isGroup = true;
    }

    String lastMessageText = '';
    if (conversation.lastMessage != null) {
      final lastMsg = conversation.lastMessage!;
      switch (lastMsg.elemType) {
        case 1: // V2TIM_ELEM_TYPE_TEXT
          lastMessageText = lastMsg.textElem?.text ?? '';
          break;
        case 3: // V2TIM_ELEM_TYPE_IMAGE
          lastMessageText = '[图片]';
          break;
        case 4: // V2TIM_ELEM_TYPE_SOUND
          lastMessageText = '[语音]';
          break;
        case 5: // V2TIM_ELEM_TYPE_VIDEO
          lastMessageText = '[视频]';
          break;
        case 6: // V2TIM_ELEM_TYPE_FILE
          lastMessageText = '[文件]';
          break;
        default:
          lastMessageText = '[消息]';
      }
    }

    return ChatConversation(
      originalConversation: conversation,
      displayName: displayName,
      avatar: avatar,
      lastMessageText: lastMessageText,
      lastMessageTime: DateTime.fromMillisecondsSinceEpoch(
        (conversation.lastMessage?.timestamp ?? 0) * 1000,
      ),
      unreadCount: conversation.unreadCount ?? 0,
      isGroup: isGroup,
    );
  }

  String get conversationId => originalConversation.conversationID ?? '';
  String get targetId => isGroup 
      ? (originalConversation.groupID ?? '') 
      : (originalConversation.userID ?? '');
}