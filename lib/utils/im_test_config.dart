/// IM测试配置
/// 这个文件用于快速测试IM功能
class IMTestConfig {
  // 测试用户配置
  static const Map<String, String> testUsers = {
    'user1': 'user1_test_sig', // 请替换为真实的UserSig
    'user2': 'user2_test_sig', // 请替换为真实的UserSig
    'user3': 'user3_test_sig', // 请替换为真实的UserSig
  };
  
  /// 获取测试用户列表
  static List<String> getTestUserIds() {
    return testUsers.keys.toList();
  }
  
  /// 获取测试用户的UserSig
  static String? getTestUserSig(String userId) {
    return testUsers[userId];
  }
  
  /// 检查是否为测试用户
  static bool isTestUser(String userId) {
    return testUsers.containsKey(userId);
  }
  
  /// 获取测试用户提示信息
  static String getTestUserTip() {
    return '''
测试用户配置：
${testUsers.keys.map((userId) => '- $userId').join('\n')}

使用方法：
1. 在腾讯云控制台为每个测试用户生成UserSig
2. 将生成的UserSig替换到 lib/utils/im_test_config.dart 中
3. 使用测试用户ID登录进行测试
''';
  }
}