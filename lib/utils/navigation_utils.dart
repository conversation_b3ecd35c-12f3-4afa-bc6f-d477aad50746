import 'package:flutter/material.dart';
import 'package:xk_app/components/app_web_page.dart';
import 'package:xk_app/components/web_page.dart';
import 'package:xk_app/sdk/http.dart';

class NavigationUtils {
  /// 智能导航方法
  /// 如果是 /pages/ 或者 http: https: 开头的页面，使用 WebView 跳转
  /// 其他的使用内置路由跳转
  static Future<void> navigateTo(BuildContext context, String url, {Map<String, dynamic>? arguments}) async {
    if (_isWebUrl(url)) {
      // 使用 WebView 跳转
      await Navigator.of(context).push(
        MaterialPageRoute(
          // builder: (context) => WebPage(
          builder: (context) => AppWebPage(
            url: url.startsWith("/pages") ? h5Host + url : url,
            title: null, // 不设置固定标题，让它动态更新
            onPageStarted: (url) {
              debugPrint('🚀 开始加载: $url');
            },
            onPageFinished: (url) {
              debugPrint('✅ 加载完成: $url');
            },
            onWebResourceError: (error) {
              // debugPrint('❌ 加载错误: ${error.description}');
              debugPrint('❌ 加载错误: ${error}');
            },
          ),
        ),
      );
    } else {
      // 使用内置路由跳转
      await Navigator.pushNamed(
        context,
        url,
        arguments: arguments,
      );
    }
  }

  /// 判断是否为需要使用 WebView 的 URL
  static bool _isWebUrl(String url) {
    return url.startsWith('/pages/') || 
           url.startsWith('http:') || 
           url.startsWith('https:');
  }

  /// 构建带参数的 URL
  static String buildUrlWithParams(String baseUrl, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return baseUrl;
    }

    final uri = Uri.parse(baseUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);
    
    // 添加新参数
    params.forEach((key, value) {
      queryParams[key] = value.toString();
    });

    return uri.replace(queryParameters: queryParams).toString();
  }

  /// 便捷方法：跳转到社区详情页
  static Future<void> navigateToCommunityDetail(BuildContext context, String id) async {
    final url = buildUrlWithParams('/pages/community/index.html', {'id': id});
    await navigateTo(context, url);
  }

  /// 便捷方法：跳转到公告详情页
  static Future<void> navigateToNoticeDetail(BuildContext context, String id) async {
    final url = buildUrlWithParams('/pages/notice/index.html', {'id': id});
    await navigateTo(context, url);
  }

  /// 便捷方法：跳转到外部链接
  static Future<void> navigateToExternalUrl(BuildContext context, String url) async {
    await navigateTo(context, url);
  }

  /// 便捷方法：返回上一页
  static void goBack(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// 便捷方法：返回到根页面
  static void goToRoot(BuildContext context) {
    Navigator.popUntil(context, (route) => route.isFirst);
  }

  /// 便捷方法：替换当前页面
  static Future<void> replaceTo(BuildContext context, String url, {Map<String, dynamic>? arguments}) async {
    if (_isWebUrl(url)) {
      await Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => WebPage(
          // builder: (context) => AppWebPage(
            url: url.startsWith("/pages") ? h5Host + url : url,
            title: null,
            onPageStarted: (url) {
              debugPrint('🚀 开始加载: $url');
            },
            onPageFinished: (url) {
              debugPrint('✅ 加载完成: $url');
            },
            onWebResourceError: (error) {
              debugPrint('❌ 加载错误: ${error}');
            },
          ),
        ),
      );
    } else {
      await Navigator.pushReplacementNamed(
        context,
        url,
        arguments: arguments,
      );
    }
  }
}