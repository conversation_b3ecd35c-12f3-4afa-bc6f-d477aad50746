import 'package:flutter/material.dart';
import '../components/web_page.dart';

class WebUtils {
  /// 打开WebView页面的通用方法
  static Future<void> openWebPage(
    BuildContext context, {
    required String url,
    String? title,
    bool showAppBar = true,
    bool enableJavaScript = true,
    bool enableDomStorage = true,
    Function(String)? onPageStarted,
    Function(String)? onPageFinished,
  }) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebPage(
          url: url,
          title: title,
          showAppBar: showAppBar,
          enableJavaScript: enableJavaScript,
          enableDomStorage: enableDomStorage,
          onPageStarted: onPageStarted,
          onPageFinished: onPageFinished,
        ),
      ),
    );
  }

  /// 验证URL格式
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 格式化URL，确保有协议前缀
  static String formatUrl(String url) {
    if (url.isEmpty) return url;
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'https://$url';
    }
    return url;
  }

  /// 从URL中提取域名
  static String? extractDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return null;
    }
  }
}