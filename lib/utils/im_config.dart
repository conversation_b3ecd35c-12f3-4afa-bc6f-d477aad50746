/// 腾讯IM配置类
class IMConfig {
  // 请在腾讯云控制台获取您的SDKAppID
  // https://console.cloud.tencent.com/im
  static const int sdkAppId = 1600002570; // 请替换为您的真实SDKAppID
  
  // UserSig生成说明：
  // UserSig是腾讯云设计的一种安全保护签名，目的是为了阻止恶意攻击者盗用您的云服务使用权。
  // 具体的计算方法是对SDKAppID、UserID和EXPIRETIME进行加密，算法为HMAC-SHA256。
  // 
  // 在开发阶段，您可以在腾讯云控制台生成UserSig：
  // https://console.cloud.tencent.com/im/tool-usersig
  // 
  // 在生产环境中，建议您将UserSig的计算代码集成到您的服务端，
  // 并提供面向App的接口，在需要UserSig时由您的App向业务服务器发起请求获取动态UserSig。
  
  /// 检查配置是否有效
  static bool isConfigValid() {
    return sdkAppId > 0;
  }
  
  /// 获取配置提示信息
  static String getConfigTip() {
    if (!isConfigValid()) {
      return '''
请先配置腾讯IM：

1. 在腾讯云控制台创建IM应用：
   https://console.cloud.tencent.com/im

2. 获取SDKAppID，并在 lib/utils/im_config.dart 中配置

3. 在控制台生成UserSig用于测试：
   https://console.cloud.tencent.com/im/tool-usersig

注意：生产环境请在服务端生成UserSig
''';
    }
    return '';
  }
}