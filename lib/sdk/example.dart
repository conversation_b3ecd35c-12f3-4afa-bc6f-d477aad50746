import 'index.dart';

/// SDK使用示例
class ApiExample {
  final ApiService api = ApiService();

  /// 用户登录示例
  Future<void> loginExample() async {
    try {
      final response = await api.user.login(
        email: '<EMAIL>',
        pwd: 'password123',
      );
      
      if (response.isSuccess) {
        print('登录成功: ${response.data}');
        // 保存token到全局变量
        token = response.data ?? '';
      } else {
        print('登录失败: ${response.msg}');
      }
    } catch (e) {
      print('登录异常: $e');
    }
  }

  /// 获取用户信息示例
  Future<void> getUserInfoExample() async {
    try {
      final response = await api.user.getUserInfo();
      
      if (response.isSuccess && response.data != null) {
        final userInfo = response.data!;
        print('用户ID: ${userInfo.id}');
        print('昵称: ${userInfo.nickName}');
        print('头像: ${userInfo.avatar}');
      } else {
        print('获取用户信息失败: ${response.msg}');
      }
    } catch (e) {
      print('获取用户信息异常: $e');
    }
  }

  /// 获取社区文章列表示例
  Future<void> getCommunityListExample() async {
    try {
      final response = await api.community.getAnnounceList(
        currentPage: 1,
        pageSize: 20,
      );
      
      if (response.isSuccess && response.data != null) {
        final pageData = response.data!;
        print('总数: ${pageData.total}');
        print('文章数量: ${pageData.records.length}');
        
        for (final article in pageData.records) {
          print('文章标题: ${article.name}');
          print('点赞数: ${article.likeTimes}');
          print('评论数: ${article.commentTimes}');
        }
      } else {
        print('获取社区文章失败: ${response.msg}');
      }
    } catch (e) {
      print('获取社区文章异常: $e');
    }
  }

  /// 发布文章示例
  Future<void> publishArticleExample() async {
    try {
      final response = await api.community.publishAnnounce(
        thumb: 'https://example.com/image.jpg',
        name: '我的第一篇文章',
        content: '这是文章内容...',
      );
      
      if (response.isSuccess) {
        print('发布成功: ${response.data}');
      } else {
        print('发布失败: ${response.msg}');
      }
    } catch (e) {
      print('发布异常: $e');
    }
  }

  /// 点赞文章示例
  Future<void> likeArticleExample(int announceId) async {
    try {
      final response = await api.community.likeAnnounce(
        announceId: announceId,
      );
      
      if (response.isSuccess) {
        print('点赞操作成功: ${response.data}');
      } else {
        print('点赞操作失败: ${response.msg}');
      }
    } catch (e) {
      print('点赞操作异常: $e');
    }
  }

  /// 评论文章示例
  Future<void> commentArticleExample(int announceId) async {
    try {
      final response = await api.community.commentAnnounce(
        announceId: announceId,
        content: '这是一条评论',
      );
      
      if (response.isSuccess) {
        print('评论成功: ${response.data}');
      } else {
        print('评论失败: ${response.msg}');
      }
    } catch (e) {
      print('评论异常: $e');
    }
  }

  /// 获取广告列表示例
  Future<void> getAdvertListExample() async {
    try {
      final response = await api.advert.getAdvertList();
      
      if (response.isSuccess && response.data != null) {
        final adverts = response.data!;
        print('广告数量: ${adverts.length}');
        
        for (final advert in adverts) {
          print('广告名称: ${advert.name}');
          print('广告图片: ${advert.img}');
          print('广告链接: ${advert.link}');
        }
      } else {
        print('获取广告列表失败: ${response.msg}');
      }
    } catch (e) {
      print('获取广告列表异常: $e');
    }
  }

  /// 获取商品列表示例
  Future<void> getProductsListExample() async {
    try {
      final response = await api.products.getProductsList(
        currentPage: 1,
        pageSize: 10,
      );
      
      if (response.isSuccess && response.data != null) {
        final pageData = response.data!;
        print('商品总数: ${pageData.total}');
        
        for (final product in pageData.records) {
          print('商品标题: ${product.title}');
          print('商品图片: ${product.thumb}');
          print('商品链接: ${product.link}');
        }
      } else {
        print('获取商品列表失败: ${response.msg}');
      }
    } catch (e) {
      print('获取商品列表异常: $e');
    }
  }

  /// 提交反馈示例
  Future<void> submitFeedbackExample() async {
    try {
      final response = await api.feedback.submitFeedback(
        email: '<EMAIL>',
        question: '我遇到了一个问题...',
        socialMedia: '@username',
        orderNum: 'ORDER123456',
      );
      
      if (response.isSuccess) {
        print('反馈提交成功');
      } else {
        print('反馈提交失败: ${response.msg}');
      }
    } catch (e) {
      print('反馈提交异常: $e');
    }
  }

  /// 举报用户示例
  Future<void> reportUserExample() async {
    try {
      final response = await api.report.reportUser(
        targetId: 'user123',
        reason: '发布不当内容',
        img: 'https://example.com/evidence.jpg',
      );
      
      if (response.isSuccess) {
        print('举报成功: ${response.data}');
      } else {
        print('举报失败: ${response.msg}');
      }
    } catch (e) {
      print('举报异常: $e');
    }
  }

  /// 获取官方信息示例
  Future<void> getOfficeDataExample() async {
    try {
      final response = await api.office.getOfficeData();
      
      if (response.isSuccess && response.data != null) {
        final officeData = response.data!;
        print('公司名称: ${officeData.name}');
        print('官网: ${officeData.webSite}');
        print('邮箱: ${officeData.email}');
        print('社交账号: ${officeData.accounts}');
        print('客服账号: ${officeData.services}');
      } else {
        print('获取官方信息失败: ${response.msg}');
      }
    } catch (e) {
      print('获取官方信息异常: $e');
    }
  }
}
