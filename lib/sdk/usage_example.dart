import 'package:flutter/material.dart';
import 'index.dart';

/// 完整的SDK使用示例
class ApiUsageExample extends StatefulWidget {
  @override
  _ApiUsageExampleState createState() => _ApiUsageExampleState();
}

class _ApiUsageExampleState extends State<ApiUsageExample> {
  final ApiService api = ApiService();
  bool isLoading = false;
  String result = '';

  @override
  void initState() {
    super.initState();
    // 初始化HTTP客户端
    initHttp([]);
  }

  /// 显示结果
  void showResult(String message) {
    setState(() {
      result = message;
      isLoading = false;
    });
  }

  /// 显示加载状态
  void showLoading() {
    setState(() {
      isLoading = true;
      result = '';
    });
  }

  /// 用户登录示例
  Future<void> loginExample() async {
    showLoading();
    try {
      final response = await api.user.login(
        email: '<EMAIL>',
        pwd: 'password123',
      );
      
      if (response.isSuccess) {
        token = response.data ?? '';
        showResult('登录成功，Token: ${response.data}');
      } else {
        showResult('登录失败: ${response.msg}');
      }
    } catch (e) {
      showResult('登录异常: $e');
    }
  }

  /// 获取社区文章列表示例
  Future<void> getCommunityListExample() async {
    showLoading();
    try {
      final response = await api.community.getAnnounceList(
        currentPage: 1,
        pageSize: 10,
      );
      
      if (response.isSuccess && response.data != null) {
        final pageData = response.data!;
        final articles = pageData.records;
        
        String resultText = '获取成功！\n';
        resultText += '总数: ${pageData.total}\n';
        resultText += '当前页数据: ${articles.length}\n\n';
        
        for (int i = 0; i < articles.length && i < 3; i++) {
          final article = articles[i];
          resultText += '文章 ${i + 1}:\n';
          resultText += '  标题: ${article.name ?? "无标题"}\n';
          resultText += '  点赞数: ${article.likeTimes ?? 0}\n';
          resultText += '  评论数: ${article.commentTimes ?? 0}\n\n';
        }
        
        showResult(resultText);
      } else {
        showResult('获取失败: ${response.msg}');
      }
    } catch (e) {
      showResult('获取异常: $e');
    }
  }

  /// 获取广告列表示例
  Future<void> getAdvertListExample() async {
    showLoading();
    try {
      final response = await api.advert.getAdvertList();
      
      if (response.isSuccess && response.data != null) {
        final adverts = response.data!;
        
        String resultText = '广告列表获取成功！\n';
        resultText += '广告数量: ${adverts.length}\n\n';
        
        for (int i = 0; i < adverts.length && i < 3; i++) {
          final advert = adverts[i];
          resultText += '广告 ${i + 1}:\n';
          resultText += '  名称: ${advert.name ?? "无名称"}\n';
          resultText += '  类型: ${advert.type ?? 0}\n';
          resultText += '  链接: ${advert.link ?? "无链接"}\n\n';
        }
        
        showResult(resultText);
      } else {
        showResult('获取广告失败: ${response.msg}');
      }
    } catch (e) {
      showResult('获取广告异常: $e');
    }
  }

  /// 获取用户信息示例
  Future<void> getUserInfoExample() async {
    showLoading();
    try {
      final response = await api.user.getUserInfo();
      
      if (response.isSuccess && response.data != null) {
        final userInfo = response.data!;
        
        String resultText = '用户信息获取成功！\n';
        resultText += 'ID: ${userInfo.id ?? "未知"}\n';
        resultText += '昵称: ${userInfo.nickName ?? "未设置"}\n';
        resultText += '账号: ${userInfo.account ?? "未知"}\n';
        resultText += '国家: ${userInfo.country ?? "未设置"}\n';
        resultText += '性别: ${_getSexText(userInfo.sex)}\n';
        resultText += '身高: ${userInfo.height ?? 0} cm\n';
        resultText += '体重: ${userInfo.weight ?? 0} kg\n';
        resultText += '登录状态: ${userInfo.isLogin == true ? "已登录" : "未登录"}\n';
        
        showResult(resultText);
      } else {
        showResult('获取用户信息失败: ${response.msg}');
      }
    } catch (e) {
      showResult('获取用户信息异常: $e');
    }
  }

  /// 获取商品列表示例
  Future<void> getProductsListExample() async {
    showLoading();
    try {
      final response = await api.products.getProductsList(
        currentPage: 1,
        pageSize: 5,
      );
      
      if (response.isSuccess && response.data != null) {
        final pageData = response.data!;
        final products = pageData.records;
        
        String resultText = '商品列表获取成功！\n';
        resultText += '总数: ${pageData.total}\n';
        resultText += '当前页数据: ${products.length}\n\n';
        
        for (int i = 0; i < products.length && i < 3; i++) {
          final product = products[i];
          resultText += '商品 ${i + 1}:\n';
          resultText += '  标题: ${product.title ?? "无标题"}\n';
          resultText += '  类型: ${product.type ?? "未知"}\n';
          resultText += '  审核状态: ${_getCheckStatusText(product.checkStatus)}\n\n';
        }
        
        showResult(resultText);
      } else {
        showResult('获取商品失败: ${response.msg}');
      }
    } catch (e) {
      showResult('获取商品异常: $e');
    }
  }

  /// 获取性别文本
  String _getSexText(int? sex) {
    switch (sex) {
      case 0:
        return '男';
      case 1:
        return '女';
      case 2:
        return '无性别';
      default:
        return '未设置';
    }
  }

  /// 获取审核状态文本
  String _getCheckStatusText(int? status) {
    switch (status) {
      case 0:
        return '待审核';
      case 1:
        return '审核通过';
      case 2:
        return '审核不通过';
      default:
        return '未知状态';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('API SDK 使用示例'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'API 测试示例',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            
            // 按钮组
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                ElevatedButton(
                  onPressed: isLoading ? null : loginExample,
                  child: Text('用户登录'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : getUserInfoExample,
                  child: Text('获取用户信息'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : getCommunityListExample,
                  child: Text('社区文章'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : getAdvertListExample,
                  child: Text('广告列表'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : getProductsListExample,
                  child: Text('商品列表'),
                ),
              ],
            ),
            
            SizedBox(height: 20),
            
            // 结果显示区域
            Expanded(
              child: Container(
                padding: EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: isLoading
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('请求中...'),
                          ],
                        ),
                      )
                    : SingleChildScrollView(
                        child: Text(
                          result.isEmpty ? '点击上方按钮测试API功能' : result,
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
