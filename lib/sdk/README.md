# Acvioo API SDK

这是为 Acvioo 用户端 API 生成的 Dart SDK，基于 OpenAPI 3.0.3 规范。

## 功能特性

- 🚀 完整的 API 覆盖
- 📱 类型安全的 Dart 模型
- 🔄 统一的响应处理
- 📄 分页数据支持
- 🛡️ 错误处理机制
- 📝 详细的使用示例

## 快速开始

### 1. 初始化 HTTP 客户端

```dart
import 'package:your_app/sdk/index.dart';

void main() {
  // 初始化HTTP客户端
  initHttp([]);
  
  // 设置全局token（登录后）
  token = 'your_token_here';
}
```

### 2. 基本使用

```dart
final api = ApiService();

// 用户登录
final loginResponse = await api.user.login(
  email: '<EMAIL>',
  pwd: 'password123',
);

if (loginResponse.isSuccess) {
  token = loginResponse.data ?? '';
  print('登录成功');
}
```

## API 模块

### 用户模块 (UserApi)

```dart
// 登录
await api.user.login(email: 'email', pwd: 'password');

// 注册
await api.user.register(email: 'email', pwd: 'password', code: 'code');

// 获取用户信息
await api.user.getUserInfo();

// 修改头像
await api.user.changeAvatar(avatar: 'avatar_url');

// 修改用户信息
await api.user.changeInfo(nickName: 'nickname');

// 修改密码
await api.user.changePassword(pwd: 'old', newPwd: 'new');
```

### 社区模块 (CommunityApi)

```dart
// 获取文章列表
await api.community.getAnnounceList(currentPage: 1, pageSize: 20);

// 获取文章详情
await api.community.getAnnounceDetail(id: 123);

// 发布文章
await api.community.publishAnnounce(
  thumb: 'image_url',
  name: 'title',
  content: 'content',
);

// 点赞文章
await api.community.likeAnnounce(announceId: 123);

// 评论文章
await api.community.commentAnnounce(announceId: 123, content: 'comment');
```

### 官方社区模块 (OfficialCommunityApi)

```dart
// 获取官方文章列表
await api.officialCommunity.getAnnounceList();

// 获取官方文章详情
await api.officialCommunity.getAnnounceDetail(id: 123);

// 评论官方文章
await api.officialCommunity.commentAnnounce(announceId: 123, content: 'comment');
```

### 广告模块 (AdvertApi)

```dart
// 获取广告列表
await api.advert.getAdvertList();
```

### 文章模块 (NoticeApi)

```dart
// 获取文章列表
await api.notice.getNoticeList(type: 0); // 0-公告 1-协议 2-指南

// 获取文章详情
await api.notice.getNoticeDetail(id: 123);
```

### 站内信模块 (MessageApi)

```dart
// 获取站内信列表
await api.message.getMessageList();

// 获取站内信详情
await api.message.getMessageDetail(id: 123);
```

### 音乐模块 (MusicApi)

```dart
// 获取音乐列表
await api.music.getMusicList();
```

### 反馈模块 (FeedbackApi)

```dart
// 提交反馈
await api.feedback.submitFeedback(
  email: 'email',
  question: 'question',
  socialMedia: 'social',
);
```

### 商品模块 (ProductsApi)

```dart
// 获取商品分类
await api.products.getCategoryList();

// 获取商品列表
await api.products.getProductsList();

// 获取商品详情
await api.products.getProductDetail(id: 123);
```

### 举报模块 (ReportApi)

```dart
// 举报用户
await api.report.reportUser(
  targetId: 'user_id',
  reason: 'reason',
  img: 'evidence_url',
);
```

### 官方客服模块 (OfficeApi)

```dart
// 获取客服信息
await api.office.getOfficeData();

// 获取官方信息
await api.office.getOfficialData();
```

### 数据统计模块 (DataApi)

```dart
// 更新数据统计
await api.data.updateData(
  regNum: 1,
  bindNum: 1,
  stopChatTime: 60,
);
```

## 响应处理

所有 API 返回统一的 `BaseResponse<T>` 格式：

```dart
class BaseResponse<T> {
  final int code;        // 状态码
  final String? msg;     // 错误信息
  final T? data;         // 数据

  bool get isSuccess => code == 200;
}
```

### 分页响应

列表接口返回 `PageResponse<T>` 格式：

```dart
class PageResponse<T> {
  final List<T> records;  // 数据列表
  final int total;        // 总数
}
```

## 错误处理

```dart
try {
  final response = await api.user.login(email: 'email', pwd: 'pwd');
  
  if (response.isSuccess) {
    // 成功处理
    print('Success: ${response.data}');
  } else {
    // 业务错误
    print('Error: ${response.msg}');
  }
} catch (e) {
  // 网络或其他异常
  print('Exception: $e');
}
```

## 状态码说明

- `200`: 成功
- `401`: 未登录
- `402`: 资料未填写
- `403`: 账号冻结
- `-1`: 条件未满足，具体错误信息在 msg 中

## 注意事项

1. 使用前需要先调用 `initHttp([])` 初始化
2. 登录成功后需要设置全局 `token`
3. 所有需要认证的接口都会自动带上 token
4. 请求头会自动添加必要的信息（User-Agent、Accept-Language等）

## 示例代码

详细的使用示例请参考 `example.dart` 文件。
