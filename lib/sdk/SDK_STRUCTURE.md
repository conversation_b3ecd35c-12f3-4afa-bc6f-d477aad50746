# Acvioo API SDK 结构说明

## 文件结构

```
lib/sdk/
├── api.dart                 # 主要API服务类
├── http.dart               # HTTP请求工具类（已存在）
├── index.dart              # SDK导出文件
├── example.dart            # 基础使用示例
├── usage_example.dart      # Flutter UI示例
├── README.md               # 使用文档
├── SDK_STRUCTURE.md        # 本文件
└── models/                 # 数据模型目录
    ├── base_response.dart  # 基础响应模型
    ├── user_models.dart    # 用户相关模型
    ├── community_models.dart # 社区相关模型
    └── other_models.dart   # 其他模型
```

## API 模块分类

### 1. 用户模块 (UserApi)
- **登录/注册**: `login()`, `register()`, `logout()`
- **用户信息**: `getUserInfo()`, `changeInfo()`, `changeAvatar()`
- **密码管理**: `changePassword()`, `findPassword()`
- **邮箱绑定**: `bindEmail()`
- **账号管理**: `deleteAccount()`
- **IM签名**: `generateImSign()`
- **资金相关**: `getUserMoney()`, `getUserMoneyLogs()`
- **设备管理**: `getUserDevices()`
- **关系管理**: `getUserRelations()`
- **互动记录**: `getUserPlayLogs()`

### 2. 社区模块 (CommunityApi) - 世界
- **文章管理**: `getAnnounceList()`, `getMyAnnounceList()`, `getAnnounceDetail()`
- **发布内容**: `publishAnnounce()`, `deleteAnnounce()`
- **互动功能**: `likeAnnounce()`, `collectAnnounce()`
- **评论系统**: `commentAnnounce()`, `replyComment()`, `getCommentList()`, `getReplyList()`
- **消息管理**: `getUnreadCount()`, `getLatestMessage()`, `getMessageList()`
- **收藏管理**: `getCollectList()`, `getLikeList()`

### 3. 官方社区模块 (OfficialCommunityApi)
- **官方内容**: `getAnnounceList()`, `getAnnounceDetail()`
- **互动功能**: `likeAnnounce()`, `commentAnnounce()`, `replyComment()`
- **评论系统**: `getCommentList()`, `getReplyList()`
- **消息管理**: `getUnreadCount()`, `getLatestMessage()`, `getMyMessageList()`

### 4. 广告模块 (AdvertApi)
- **广告展示**: `getAdvertList()`

### 5. 文章模块 (NoticeApi)
- **文章管理**: `getNoticeList()`, `getNoticeDetail()`
- **类型支持**: 公告(0)、协议(1)、指南(2)

### 6. 站内信模块 (MessageApi)
- **消息管理**: `getMessageList()`, `getMessageDetail()`

### 7. 音乐模块 (MusicApi)
- **音乐列表**: `getMusicList()`

### 8. 反馈模块 (FeedbackApi)
- **用户反馈**: `submitFeedback()`

### 9. 商品模块 (ProductsApi)
- **分类管理**: `getCategoryList()`, `getSubCategories()`
- **商品管理**: `getProductsList()`, `getProductDetail()`
- **运营组**: `getSellerInfo()`

### 10. 举报模块 (ReportApi)
- **用户举报**: `reportUser()`

### 11. 官方客服模块 (OfficeApi)
- **客服信息**: `getOfficeData()`, `getOfficialData()`

### 12. 数据统计模块 (DataApi)
- **数据上报**: `updateData()`

### 13. 版本更新模块 (VersionApi)
- **版本检查**: `checkUpdate()`

### 14. 测试模块 (TestApi)
- **测试功能**: `clearSession()`

### 15. 小程序模块 (WxApi)
- **小程序相关**: `getWxData()`

### 16. 波图模块 (WaveApi)
- **波图功能**: `getWaveData()`

## 数据模型分类

### 基础模型 (base_response.dart)
- `BaseResponse<T>`: 统一响应格式
- `PageResponse<T>`: 分页响应格式
- `PageRequest`: 分页请求参数

### 用户模型 (user_models.dart)
- `User`: 用户完整信息
- `UserInfoResult`: 用户信息返回结果
- `UserMoneyResult`: 用户资金信息
- `UserRelation`: 用户关系
- `UserDevice`: 用户玩具设备
- `UserMoneyPointsLog`: 用户资金明细
- `UserPlayLog`: 用户互动记录

### 社区模型 (community_models.dart)
- `SellAnnounce`: 社区文章
- `SellAnnounceComment`: 文章评论
- `SellAnnounceReply`: 评论回复
- `SellAnnounceCollect`: 文章收藏
- `SellAnnounceMsg`: 社区消息
- `SellAnnounceLike`: 点赞记录

### 其他模型 (other_models.dart)
- `Advert`: 广告信息
- `Notice`: 通知/文章
- `Message`: 站内信
- `Music`: 音乐信息
- `Feedback`: 用户反馈
- `SellerProductsCate`: 商品分类
- `SellerProducts`: 商品信息
- `Seller`: 运营组信息
- `OfficeData`: 官方信息
- `VersionUpdateResult`: 版本更新信息
- `SellerDevice`: 运营组玩具

## 状态码说明

- `200`: 操作成功
- `401`: 用户未登录
- `402`: 用户资料未填写完整
- `403`: 用户账号被冻结
- `-1`: 业务逻辑错误，具体信息在 `msg` 字段中

## 使用流程

1. **初始化**: 调用 `initHttp([])` 初始化HTTP客户端
2. **登录**: 使用 `api.user.login()` 进行用户登录
3. **设置Token**: 登录成功后设置全局 `token` 变量
4. **调用API**: 使用各个模块的API方法
5. **处理响应**: 检查 `response.isSuccess` 和处理 `response.data`

## 错误处理

```dart
try {
  final response = await api.user.login(email: 'email', pwd: 'pwd');
  
  if (response.isSuccess) {
    // 成功处理
  } else {
    // 业务错误处理
    print('Error: ${response.msg}');
  }
} catch (e) {
  // 网络或其他异常处理
  print('Exception: $e');
}
```

## 注意事项

1. 所有API调用都是异步的，需要使用 `await`
2. 需要登录的接口会自动携带token
3. 分页接口默认参数：`currentPage: 1`, `pageSize: 20`
4. 所有时间字段都是字符串格式
5. 布尔字段可能为null，使用时需要判断
6. 数值字段建议使用 `?.toDouble()` 或 `?? 0` 进行安全转换

## 扩展说明

如需添加新的API接口：

1. 在对应的API类中添加方法
2. 如果需要新的数据模型，在 `models/` 目录下添加
3. 在 `index.dart` 中导出新的模型
4. 更新文档和示例

SDK已经覆盖了API文档中的所有接口，可以直接使用。
