import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

// const h5Host = 'http://*************:5173';
// const h5Host = 'http://*************:8000';
const h5Host = 'http://***************';
const host = 'http://*************:8000';
// const host = 'http://*************:7777';
const cdn = 'https://acvioo.oss-cn-shenzhen.aliyuncs.com';
const timeout = Duration(seconds: 10);
var token = '';
// TODO test token
// var token = '96d9e73d020a40969af2fa95a1e49f51';
var globalLang = 'zh_CN';
const version = '1.0.0';

// 更新全局token的方法
void updateToken(String newToken) {
  print('更新全局token: $newToken');
  token = newToken;
}

late Dio dio;

Future<bool> initHttp(List<Interceptor> list) async {
  dio = Dio();
  if (kDebugMode) {
    // 抓包代理设置，开发DEBUG模式
    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   client.findProxy = (uri) {
    //     return "PROXY ************:18888";
    //   };
    //   // 忽略证书
    //   client.badCertificateCallback = (c, h, p) => true;
    // };
  }

  print('初始化');

  final prefs = await SharedPreferences.getInstance();
  token = await prefs.getString('user_token') ?? '';

  list.forEach((element) {
    dio.interceptors.add(element);
  });

  return true;
}

Map<String, String> getHeaders() {
  return {
    'client': 'APP',
    'token': token,
    'Accept-Language': globalLang,
    "platform": "chingga",
    'User-Agent':
        "HyApp $version ${Platform.operatingSystem} ${Platform.operatingSystemVersion} ${Platform.version}",
    'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
  };
}

Future<Map<String, dynamic>> httpPost(String url, dynamic data) async {
  var fullUrl = parseUrl(url);

  print('POST $fullUrl');

  // print(data.runtimeType.toString());
  // print();
  if (data == null || data.runtimeType.toString() == '_Map<dynamic, dynamic>') {
    data = {"": ""};
  }

  print(data);

  var resp = await dio
      .post(fullUrl,
          // data: convert.jsonEncode(data),
          data: data,
          // data: FormData.fromMap(data),
          options: Options(headers: getHeaders()))
      .timeout(timeout);
  return wrapResponse(resp);
}

Future<Map<String, dynamic>> httpGet(String url, dynamic data) async {
  var fullUrl = parseUrl(url);
  print('GET $fullUrl $data');
  var resp = await dio
      .get(fullUrl,
          queryParameters: Map<String, dynamic>.from(data),
          options: Options(headers: getHeaders()))
      .timeout(timeout);
  return wrapResponse(resp);
}

Future<Map<String, dynamic>> httpDelete(String url, dynamic data) async {
  var fullUrl = parseUrl(url);
  print('DELETE $fullUrl');
  var resp = await dio
      .delete(fullUrl,
          data: Map<String, dynamic>.from(data),
          options: Options(headers: getHeaders()))
      .timeout(timeout);
  return wrapResponse(resp);
}

Future<Map<String, dynamic>> httpPut(String url, dynamic data) async {
  var fullUrl = parseUrl(url);
  print('PUT $fullUrl');
  var resp = await dio
      .put(fullUrl,
          data: Map<String, dynamic>.from(data),
          options: Options(headers: getHeaders()))
      .timeout(timeout);
  return wrapResponse(resp);
}

String parseUrl(String url) {
  var fullUrl = url;
  if (fullUrl.indexOf("http") != 0) {
    fullUrl = host + url; // 目前用的
    // 临时用的，新增一个lu前缀
    // if (host.contains("api.lukeny.xyz")) {
    //   fullUrl = host + url.replaceFirst("/api", "/api/lu");
    // }
    // fullUrl = host + '/api' + url;
    // fullUrl = 'https://h5.ihuacloud.com' + url;
    // fullUrl = 'https://h5.ihuacloud.com' + '/api' + url;
    // fullUrl = 'http://************' + url.replaceFirst(RegExp('/huayun-erp|/huayun-mes|/huayun-pasm'), '/api');
    // fullUrl = 'http://************' + url.replaceFirst(RegExp('/huayun-'), '/api/');
    // fullUrl = 'http://************' + '/api' + url;
  }
  return fullUrl;
}

Future<Map<String, dynamic>> wrapResponse(Response<dynamic> resp) async {
  if (resp.statusCode == 200) {
    var res = resp.data as Map<String, dynamic>;
    // print('res=');
    // print(res);
    if (res["code"] == 200) {
      return res;
    } else {
      // print(res['msg']);
      // var msg = res["code"] == -1 ? (res["msg"] ?? '系统繁忙，请稍后再试') : "系统繁忙，请稍后再试";
      // var msg = '反面';
      var msg = res["msg"] ?? 'Network Timeout.';
      // toast(msg);
      // print('====请求异常====');
      // toast(msg);
      throw HyErr("$msg");
    }
  } else {
    // toast('Network Error.');
    throw HyErr("请求错误[code=${resp.statusCode}, msg=${resp.data}]");
  }
}

class HyErr extends Error {
  final String message;

  HyErr(this.message);
}