import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:xk_app/sdk/http.dart';
import 'pages/splash_page.dart';
import 'pages/main_tab_page.dart';
import 'pages/login_page.dart';
import 'pages/register_page.dart';
import 'pages/profile_setup_page.dart';
// import 'pages/web_demo_page.dart';
import 'pages/mobile_web_demo_page.dart';
import 'pages/test_interception_page.dart';
import 'pages/community_page.dart';
import 'services/im_service.dart';

import 'package:xk_app/services/webview_service.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await initHttp([]);

  // Pre-warm the WebView controller
  // No need to wait for it to complete, it can run in the background
  WebViewService().prewarmController();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => IMService(),
      child: MaterialApp(
        title: 'XK App',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF6366F1)),
          useMaterial3: true,
        ),
        home: const SplashPage(),
        routes: {
          '/home': (context) => const MainTabPage(),
          '/main': (context) => const MainTabPage(),
          '/login': (context) => const LoginPage(),
          '/register': (context) => const RegisterPage(),
          '/profile-setup': (context) => const ProfileSetupPage(),
          // '/web-demo': (context) => const WebDemoPage(),
          '/mobile-web-demo': (context) => const MobileWebDemoPage(),
          '/test-interception': (context) => const TestInterceptionPage(),
          '/community': (context) => const CommunityPage(),
        },
      ),
    );
  }
}

